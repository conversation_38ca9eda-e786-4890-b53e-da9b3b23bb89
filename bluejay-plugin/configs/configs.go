package configs

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"wildlight.gg/bluejay/utils"

	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/errors"
)

type ClientHotfixConfig struct {
	Environments map[string]map[string]string
}

type ServerHotfixConfig struct {
	Environments map[string]map[string]string
}

type ConfigRequest struct {
	Type string
}

type HotfixConfigResponse struct {
	Hotfixes map[string]string
}

type ConfigResponse struct {
	Config string
}

type PriceInfo struct {
	CurrencyType string
	FullPrice    int64
	SalePrice    int64
	UserPrice    int64
}

type ItemInfo struct {
	Id          string
	IsAvailable bool
	IsFeatured  bool
	PricingInfo PriceInfo
}

type BundleDefinition struct {
	Id          string
	Items       []string
	PricingInfo PriceInfo
}

type SectionDefinition struct {
	Id             string
	Bundles        []string
	ExpirationTime int64
	TemplateType   int
}

type TreasureTroveDefinition struct {
	PricingInfo      PriceInfo
	TroveEntitlement string
	TroveInitialPage string
	TrovePages       []string
}

type TrovePageDefinition struct {
	Items       []string
	NextPage    string
	PricingInfo PriceInfo
}

type CurrencyLimits struct {
	Limits map[string]int64
}

type StoreConfig struct {
	Bundles           map[string]BundleDefinition
	CurrencyMaxLimits map[string]int64
	Items             map[string]ItemInfo
	Sections          map[string]SectionDefinition
	TreasureTroves    map[string]TreasureTroveDefinition
	TrovePages        map[string]TrovePageDefinition
}

var (
	StoreConfigs = sync.Map{}

	ClientHotFixConfigs = sync.Map{}

	ServerHotFixConfigs = sync.Map{}

	ClientHotfixConfigPath = "config/client_hotfix/"
	ServerHotfixConfigPath = "config/server_hotfix/"
	StoreConfigPath        = "config/store/"

	CLIENT_HOTFIX = "client"
	SERVER_HOTFIX = "server"
	STORE         = "store"
)

func GetClientHotfixConfigs(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return loadHotfixFiles(ctx, payload, logger, CLIENT_HOTFIX)
}

func GetServerHotfixConfigs(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return loadHotfixFiles(ctx, payload, logger, SERVER_HOTFIX)
}

func loadHotfixFiles(ctx context.Context, payload string, logger runtime.Logger, configType string) (string, error) {
	var request ConfigRequest

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", err
	}

	logger.Info("Got request", request)

	var ConfigToUse *sync.Map
	switch configType {
	case "client":
		ConfigToUse = &ClientHotFixConfigs
	case "server":
		ConfigToUse = &ServerHotFixConfigs
	default:
		return "", runtime.NewError(fmt.Sprintf("Invalid config Type requested: %s", request.Type), 3)
	}

	env := utils.GetPlayerEnvironment(ctx)

	var hotfixFiles = map[string]string{}

	hotfixes, ok := ConfigToUse.Load(env)
	if !ok {
		if content, err := json.Marshal(HotfixConfigResponse{Hotfixes: hotfixFiles}); err != nil {
			return "", err
		} else {
			return string(content), nil
		}
	} else {
		hotfixFiles = hotfixes.(map[string]string)
	}

	if content, err := json.Marshal(HotfixConfigResponse{Hotfixes: hotfixFiles}); err != nil {
		return "", err
	} else {
		return string(content), nil
	}

}

func GetConfig(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var request ConfigRequest

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", err
	}

	if request.Type == "" {
		return "", errors.InvalidPayload
	}

	var ConfigToUse *sync.Map

	switch request.Type {
	case STORE:
		ConfigToUse = &StoreConfigs
	default:
		return "", runtime.NewError(fmt.Sprintf("Invalid config Type requested: %s", request.Type), 3)
	}

	config, err := loadConfig(ctx, ConfigToUse)

	if err != nil {
		return "", err
	}

	if configData, err := json.Marshal(config); err != nil {
		return "", err
	} else {
		if content, err := json.Marshal(ConfigResponse{Config: string(configData)}); err != nil {
			return "", err
		} else {
			return string(content), nil
		}
	}
}

func loadConfig(ctx context.Context, ConfigToUse *sync.Map) (any, error) {
	env := utils.GetPlayerEnvironment(ctx)
	config, ok := ConfigToUse.Load(env)
	if !ok {
		config, ok = ConfigToUse.Load("default")

		if !ok {
			return nil, runtime.NewError(fmt.Sprintf("Environment not found %s. No defaults loaded.", env), 3)
		}
	}
	return config, nil
}

func LoadStoreConfig(ctx context.Context) (*StoreConfig, error) {
	env := utils.GetPlayerEnvironment(ctx)
	config, ok := StoreConfigs.Load(env)
	if !ok {
		config, ok = StoreConfigs.Load("default")

		if !ok {
			return nil, runtime.NewError(fmt.Sprintf("Environment not found %s. No defaults loaded.", env), 3)
		}
	}
	return config.(*StoreConfig), nil
}
