package configs

import (
	"encoding/json"
	"fmt"
	"github.com/heroiclabs/nakama-common/runtime"
	"os"
	"path/filepath"
	"sync"
)

var configWatchers = map[string]configWatcherInterface{}

type configWatcherInterface interface {
	LoadConfigsForEnv(env string, logger runtime.Logger) error
	LoadConfigFile(env string, fullPath string, logger runtime.Logger) error
}

type configWatcherNotifyFunc[T interface{}] func(string, T) error

type ConfigWatcher[T interface{}] struct {
	config             map[string]T
	configLock         sync.RWMutex
	basePath           string
	configLoadedNotify configWatcherNotifyFunc[T]
}

func NewConfigWatcher[T interface{}](basePath string, notifyFunc configWatcherNotifyFunc[T], logger runtime.Logger) *ConfigWatcher[T] {
	config := &ConfigWatcher[T]{
		config:             make(map[string]T),
		configLock:         sync.RWMutex{},
		basePath:           basePath,
		configLoadedNotify: notifyFunc,
	}

	configWatchers[basePath] = config
	addToWatcher(basePath, logger)
	return config
}

func (c ConfigWatcher[T]) GetConfigForEnvironment(env string) (T, error) {
	defer c.configLock.RUnlock()

	c.configLock.RLock()
	config, found := c.config[env]
	if !found {
		config, found = c.config[DefaultEnvironment]
		if !found {
			var out T
			return out, fmt.Errorf("could not find configuration type %T for environment %s", out, env)
		}
	}

	return config, nil
}

func (c ConfigWatcher[T]) LoadConfigsForEnv(env string, logger runtime.Logger) error {
	path := filepath.Join(c.basePath, env)

	files, err := os.ReadDir(path)

	if err != nil {
		return fmt.Errorf("Env %s not found: %w", env, err)
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fullPath := filepath.Join(path, file.Name())
		logger.WithField("path", fullPath).Info("Loading configuration file")
		if err := c.LoadConfigFile(env, fullPath, logger); err != nil {
			return err
		}
	}

	return nil
}

func (c ConfigWatcher[T]) applyConfiguration(env string, config T) {
	defer c.configLock.Unlock()
	c.configLock.Lock()
	c.config[env] = config
}

func (c ConfigWatcher[T]) LoadConfigFile(env, fullPath string, logger runtime.Logger) error {
	data, err := os.ReadFile(fullPath)
	if err != nil {
		logger.WithField("filename", fullPath).WithField("err", err).Error("error reading configuration file")
		return err
	}

	var config T
	err = json.Unmarshal(data, &config)
	if err != nil {
		logger.WithField("filename", fullPath).WithField("err", err).WithField("data", string(data)).Error("error unmarshalling configuration file")
		return err
	}

	c.applyConfiguration(env, config)

	if c.configLoadedNotify != nil {
		c.configLoadedNotify(env, config)
	}

	logger.WithField("path", fullPath).WithField("env", env).WithField("config", config).Info("Loaded configuration file")

	return nil
}
