package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
	"github.com/heroiclabs/nakama-common/runtime"
)

var fsWatcher *fsnotify.Watcher

const DefaultEnvironment = "default"

func CreateWatcher(logger runtime.Logger) error {
	err := initDirectoriesToWatch(logger)
	if err != nil {
		return err
	}

	go watchForChanges(logger)

	return nil
}

func initDirectoriesToWatch(logger runtime.Logger) error {

	directories := []string{
		ClientHotfixConfigPath,
		ServerHotfixConfigPath,
		StoreConfigPath,
	}

	for _, basePath := range directories {
		err := addToWatcher(basePath, logger)
		if err != nil {
			return err
		}
	}

	for basePath := range configWatchers {
		err := addToWatcher(basePath, logger)
		if err != nil {
			return err
		}
	}

	if _, ok := StoreConfigs.Load("default"); !ok {
		logger.Error("No defaults found for Store Configs at path %s", StoreConfigPath)
		return runtime.NewError(fmt.Sprintf("No defaults found for Store Configs at path %s", StoreConfigPath), 3)
	}
	return nil
}

func addToWatcher(basePath string, logger runtime.Logger) error {
	if fsWatcher == nil {
		logger.Info("Creating new file watcher.")
		var err error
		fsWatcher, err = fsnotify.NewWatcher()
		if err != nil {
			return err
		}
	}

	if err := fsWatcher.Add(basePath); err != nil {
		logger.Error("Error adding path %s to file watcher. %v", basePath, err)
		return runtime.NewError(fmt.Sprintf("Error adding path %s to file watcher. %v", basePath, err), 3)
	}

	if err := loadConfigsForBasePath(basePath, logger); err != nil {
		logger.Error("Error loading configs for %s. %v", basePath, err)
		return runtime.NewError(fmt.Sprintf("Error loading configs for %s. %v", basePath, err), 3)
	}

	logger.Info("Added %s to file watcher.", basePath)
	return nil
}

func loadConfigFile(path string, config interface{}, logger runtime.Logger) error {
	logger.WithField("path", path).Info("Loading config file")
	data, err := os.ReadFile(path)
	if err != nil {
		return runtime.NewError(fmt.Sprintf("Failed to read config file: %s. Err: %v", path, err), 3)
	}

	if err = json.Unmarshal(data, config); err != nil {
		return runtime.NewError(fmt.Sprintf("Failed to parse json. Err: %v", err), 3)
	}

	return nil
}

func getConfigFileName(base string) string {
	return filepath.Base(base) + ".json"
}

func loadStoreConfigForEnv(env string, logger runtime.Logger) error {
	filename := getConfigFileName(StoreConfigPath)
	path := filepath.Join(StoreConfigPath, env, filename)

	var config StoreConfig
	if err := loadConfigFile(path, &config, logger); err != nil {
		return err
	}

	StoreConfigs.Store(env, &config)
	return nil
}

func loadHotfixConfigsForEnv(base string, env string, configs *sync.Map, logger runtime.Logger) error {
	path := filepath.Join(base, env)
	files, err := os.ReadDir(path)

	if err != nil {
		return runtime.NewError(fmt.Sprintf("Error reading directory: %s. %v", path, err), 3)
	}

	hotfixConfigs := map[string]string{}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fullPath := filepath.Join(path, file.Name())

		logger.WithField("path", fullPath).Info("Loading config file.")

		data, err := os.ReadFile(fullPath)

		if err != nil {
			return runtime.NewError(fmt.Sprintf("Error reading file: %s. %v", fullPath, err), 3)
		}

		hotfixConfigs[file.Name()] = string(data)

	}

	if len(hotfixConfigs) > 0 {
		configs.Store(env, hotfixConfigs)
	}

	return nil
}

func loadConfigsForBasePath(basePath string, logger runtime.Logger) error {
	environments, err := os.ReadDir(basePath)

	logger.Info("Found %s environments in base path %s", environments, basePath)

	if err != nil {
		logger.Error("Failed to read base path: %s. %v", basePath, err)
		return runtime.NewError(fmt.Sprintf("Failed to read base path: %s. %v", basePath, err), 3)
	}

	for _, env := range environments {
		if !env.IsDir() {
			continue
		}

		envPath := filepath.Join(basePath, env.Name())

		logger.Info("Attempting to add %s to file watcher.", envPath)

		if err := fsWatcher.Add(envPath); err != nil {
			logger.Error("Error adding path %s to file watcher. %v", basePath, err)
			return runtime.NewError(fmt.Sprintf("Error adding path %s to file watcher. %v", basePath, err), 3)
		}

		switch basePath {
		case StoreConfigPath:
			if err := loadStoreConfigForEnv(env.Name(), logger); err != nil {
				return err
			}
		case ClientHotfixConfigPath:
			if err := loadHotfixConfigsForEnv(basePath, env.Name(), &ClientHotFixConfigs, logger); err != nil {
				return err
			}
		case ServerHotfixConfigPath:
			if err := loadHotfixConfigsForEnv(basePath, env.Name(), &ServerHotFixConfigs, logger); err != nil {
				return err
			}
		default:
			watcher, found := configWatchers[basePath]
			if !found {
				return runtime.NewError(fmt.Sprintf("Invalid base path found: %s", basePath), 3)
			}

			watcher.LoadConfigsForEnv(env.Name(), logger)
		}

	}

	return nil
}

func Close() error {
	return fsWatcher.Close()
}

func handleFileChange(path string, logger runtime.Logger) {
	dir := filepath.Dir(path)
	env := filepath.Base(dir)

	logger.WithField("path", path).WithField("env", env).WithField("dir", dir).Info("handleFileChange called")

	switch {
	case strings.HasPrefix(dir, StoreConfigPath):
		loadStoreConfigForEnv(env, logger)
	case strings.HasPrefix(dir, ClientHotfixConfigPath):
		loadHotfixConfigsForEnv(ClientHotfixConfigPath, env, &ClientHotFixConfigs, logger)
	case strings.HasPrefix(dir, ServerHotfixConfigPath):
		loadHotfixConfigsForEnv(ServerHotfixConfigPath, env, &ServerHotFixConfigs, logger)
	default:
		for basePath, config := range configWatchers {
			if strings.HasPrefix(dir, basePath) {
				config.LoadConfigsForEnv(env, logger)
			}
		}
	}
}

func watchForChanges(logger runtime.Logger) {
	logger.Info("Started file watcher")

	for {
		if len(fsWatcher.WatchList()) == 0 {
			logger.Info("All files removed from file watcher. Likely new mount updated. Re-adding all known top level directories.")
			initDirectoriesToWatch(logger)
		}

		select {
		case event, ok := <-fsWatcher.Events:
			logger.WithField("event", event).WithField("isOk", ok).Info("Received filewatcher event.")
			if !ok {
				return
			}

			if event.Has(fsnotify.Create) {
				info, err := os.Stat(event.Name)

				if err == nil && info.IsDir() {
					fsWatcher.Add(event.Name)
				}
			}

			if event.Has(fsnotify.Create) || event.Has(fsnotify.Write) {
				handleFileChange(event.Name, logger)
			}

			if event.Has(fsnotify.Remove) {
				fsWatcher.Remove(event.Name)
			}
		case err, ok := <-fsWatcher.Errors:

			logger.WithField("err", err).WithField("isOk", ok).Error("Received filewatcher error.")

			if !ok {
				return
			}

			logger.Error("Watcher error: %v", err)
		}
	}
}
