package playlists

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand/v2"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/utils"
)

const defaultSkillBucket = "default"
const defaultMatchmakerConfig = "default"

type featuredPlaylist struct {
	PlaylistAlias       string `json:"playlistAlias"`
	RequiredEntitlement string `json:"requiredEntitlement"`
}

type PlaylistPresentation struct {
	HeaderId      string `json:"headerId"`
	SubheaderId   string `json:"subheaderId"`
	DescriptionId string `json:"descriptionId"`
	TexturePath   string `json:"texturePath"`
}

type Playlist struct {
	Name             string               `json:"name" jsonschema:"title=Playlist Name,required"`
	TeamSize         int                  `json:"teamSize" jsonschema:"title=Team Size,required,minimum=1"`
	TeamCount        int                  `json:"teamCount" jsonschema:"title=Number of Teams,required,minimum=2"`
	SkillBucket      string               `json:"skillBucket"`
	MatchmakerConfig string               `json:"matchmakerConfig"`
	Presentation     PlaylistPresentation `json:"presentation"`
	MapModeList      []mapMode            `json:"mapModeList" jsonschema:"required"`
}

func (playlist *Playlist) PickRandomMapMode() (string, string) {
	rand := rand.Float64()

	for i := range playlist.MapModeList {
		if playlist.MapModeList[i].CalculatedWeight >= rand {
			return playlist.MapModeList[i].MapName, playlist.MapModeList[i].ModeName
		}
	}

	return playlist.MapModeList[len(playlist.MapModeList)-1].MapName, playlist.MapModeList[len(playlist.MapModeList)-1].ModeName
}

type mapMode struct {
	MapName          string  `json:"mapName" jsonschema:"ref=bluejay-map,title=Unreal Map Name,required"`
	ModeName         string  `json:"modeName" jsonschema:"ref=bluejay-mode,title=Unreal Mode Name,required,enum=raid,enum=conquest,enum=Bluejay"`
	Weight           int     `json:"weight" jsonschema:"title=Relative Weight,required,minimum=0"`
	CalculatedWeight float64 `json:"-"`
}

type clientPlaylist struct {
	Alias        string               `json:"playlistAlias"`
	Name         string               `json:"displayName"`
	Presentation PlaylistPresentation `json:"presentation"`
	MaxTeamSize  int                  `json:"maxTeamSize"`
	MinTeamCount int                  `json:"minTeamCount"`
	MaxTeamCount int                  `json:"maxTeamCount"`
}

type clientPlaylistConfig struct {
	Hash              string
	FeaturedPlaylists []featuredPlaylist
	Playlists         []clientPlaylist
}

type SkillBucket struct {
	Name                 string  `json:"-"`
	DefaultMu            float64 `json:"defaultMu"`
	DefaultSigma         float64 `json:"defaultSigma"`
	Z                    int     `json:"z"`
	UpdateSatoriProperty bool    `json:"updateSatoriProperty"`
	options              *types.OpenSkillOptions
}

func (s *SkillBucket) GetOptions() *types.OpenSkillOptions {
	if s.options == nil {
		s.options = &types.OpenSkillOptions{
			Z:     &s.Z,
			Mu:    &s.DefaultMu,
			Sigma: &s.DefaultSigma,
		}
	}

	return s.options
}

type MatchmakerConfig struct {
	MaxTries                   int     `json:"maxTries"`
	ShuffleTries               int     `json:"shuffleTries"`
	BaseTargetScore            float64 `json:"baseTargetScore"`
	TargetScoreIncrement       float64 `json:"targetScoreIncrement"`
	BaseSkillRange             float64 `json:"baseSkillRange"`
	SkillIncrement             float64 `json:"skillIncrement"`
	BypassPlatformRestrictions bool    `json:"bypassPlatformRestrictions"`
}

type MatchmakerRegion struct {
	Region  string `json:"region"`
	DnsName string `json:"dnsName"`
	Port    int    `json:"port"`
}

type PlaylistEnvironment struct {
	Hash              string                      `json:"-"`
	FeaturedPlaylists []featuredPlaylist          `json:"featuredPlaylists"`
	Playlists         map[string]Playlist         `json:"playlists"`
	SkillBuckets      map[string]SkillBucket      `json:"skillBuckets"`
	MatchmakerConfigs map[string]MatchmakerConfig `json:"matchmakerConfigs"`
	Regions           []MatchmakerRegion          `json:"regions"`
}

const PlaylistConfigPath = "config/playlist/"

type PlaylistManager struct {
	nakamaModule runtime.NakamaModule
	logger       runtime.Logger

	// server playlist configurations
	lastConfigHash string
	config         *configs.ConfigWatcher[PlaylistEnvironment]

	// client playlist configurations
	clientPlaylists map[string]*clientPlaylistConfig
}

func NewPlaylistManager(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule) (*PlaylistManager, error) {
	playlistManager := &PlaylistManager{
		nakamaModule:    nk,
		logger:          logger,
		clientPlaylists: map[string]*clientPlaylistConfig{},
	}

	playlistManager.config = configs.NewConfigWatcher[PlaylistEnvironment](PlaylistConfigPath, playlistManager.onConfigLoaded, logger)

	return playlistManager, nil
}

func (pm *PlaylistManager) onConfigLoaded(envName string, playlistConfig PlaylistEnvironment) error {
	pm.calculatePlaylistWeights(playlistConfig.Playlists)

	jsonConfig, err := json.Marshal(playlistConfig.Playlists)
	if err != nil {
		pm.logger.WithField("err", err).Error("Error marshalling playlist config")
		return err
	}

	hasher := sha256.New()
	_, err = hasher.Write([]byte(jsonConfig))
	if err != nil {
		pm.logger.WithField("err", err).Error("Error hashing playlist config")
		return fmt.Errorf("error hashing playlist configuration: %w", err)
	}

	newConfigHash := base64.StdEncoding.EncodeToString(hasher.Sum(nil))
	envClientPlaylists := clientPlaylistConfig{
		Hash:              newConfigHash,
		FeaturedPlaylists: playlistConfig.FeaturedPlaylists,
		Playlists:         []clientPlaylist{},
	}

	for playlistAlias, playlist := range playlistConfig.Playlists {
		clientPlaylist := clientPlaylist{
			Alias:        playlistAlias,
			Name:         playlist.Name,
			Presentation: playlist.Presentation,
			MaxTeamSize:  playlist.TeamSize,
			MinTeamCount: playlist.TeamCount,
			MaxTeamCount: playlist.TeamCount,
		}

		envClientPlaylists.Playlists = append(envClientPlaylists.Playlists, clientPlaylist)
	}

	pm.clientPlaylists[envName] = &envClientPlaylists
	return nil
}

func (pm *PlaylistManager) OnSessionStart(ctx context.Context, logger runtime.Logger, userId string, env string) {
	clientConfig, found := pm.clientPlaylists[env]
	if !found {
		clientConfig, found = pm.clientPlaylists[configs.DefaultEnvironment]
		if !found {
			logger.WithField("env", env).Warn("could not find playlists for environment or default")
		}
	}

	regions, err := pm.GetMatchmakerRegionsForEnvironment(env)
	if err != nil {
		logger.WithField("env", env).Warn("could not get matchmaker regions for environment or default")
		regions = nil
	}

	msg := map[string]interface{}{
		"config":  clientConfig,
		"regions": regions,
	}

	logger.WithField("userId", userId).Info("Sending playlist configuration")
	pm.nakamaModule.NotificationSend(ctx, userId, "config", msg, utils.PLAYLIST_CONFIG, utils.SystemUserId, false)
}

func (pm *PlaylistManager) GetClientPlaylistsForEnvironment(env string) (*clientPlaylistConfig, error) {
	playlists, found := pm.clientPlaylists[env]
	if !found {
		playlists, found = pm.clientPlaylists[configs.DefaultEnvironment]
		if !found {
			return nil, fmt.Errorf("could not find playlists for environment %s", env)
		}
	}

	return playlists, nil
}

func (pm *PlaylistManager) GetMatchmakerRegionsForEnvironment(env string) ([]MatchmakerRegion, error) {
	playlistConfig, err := pm.config.GetConfigForEnvironment(env)
	if err != nil {
		return nil, err
	}

	return playlistConfig.Regions, nil
}

func (pm *PlaylistManager) GetPlaylistConfigs(playlistName string, env string) (*Playlist, *SkillBucket, *MatchmakerConfig, error) {
	playlistConfig, err := pm.config.GetConfigForEnvironment(env)
	if err != nil {
		return nil, nil, nil, err
	}

	playlist, found := playlistConfig.Playlists[playlistName]
	if !found {
		return nil, nil, nil, fmt.Errorf("could not find playlist %s for environment %s", playlistName, env)
	}

	skillBucketName := playlist.SkillBucket
	if skillBucketName == "" {
		skillBucketName = defaultSkillBucket
	}

	skillBucket, found := playlistConfig.SkillBuckets[skillBucketName]
	if !found {
		return nil, nil, nil, fmt.Errorf("could not find skill bucket configuration %s for environment %s", skillBucketName, env)
	}
	skillBucket.Name = skillBucketName

	matchmakerConfigName := playlist.MatchmakerConfig
	if matchmakerConfigName == "" {
		matchmakerConfigName = defaultMatchmakerConfig
	}

	matchmakerConfig, found := playlistConfig.MatchmakerConfigs[matchmakerConfigName]
	if !found {
		return nil, nil, nil, fmt.Errorf("could not find matchmaker configuration %s for environment %s", matchmakerConfigName, env)
	}

	return &playlist, &skillBucket, &matchmakerConfig, nil
}

func (pm *PlaylistManager) GetPlaylistConfigForEnvironment(playlistName string, env string) (*Playlist, error) {
	playlistConfig, err := pm.config.GetConfigForEnvironment(env)
	if err != nil {
		return nil, err
	}

	playlist, found := playlistConfig.Playlists[playlistName]
	if !found {
		return nil, fmt.Errorf("playlist not found: %s", playlistName)
	}

	return &playlist, nil
}

func (pm *PlaylistManager) GetSkillBucketForEnvironment(skillBucketName string, env string) (*SkillBucket, error) {
	playlistConfig, err := pm.config.GetConfigForEnvironment(env)
	if err != nil {
		return nil, err
	}

	if skillBucketName == "" {
		skillBucketName = defaultSkillBucket
	}

	skillBucket, found := playlistConfig.SkillBuckets[skillBucketName]
	if !found {
		return nil, fmt.Errorf("skill bucket not found: %s", skillBucketName)
	}
	skillBucket.Name = skillBucketName

	return &skillBucket, nil
}

func (pm *PlaylistManager) GetMatchmakerConfigForEnvironment(matchmakerConfigName string, env string) (*MatchmakerConfig, error) {
	playlistConfig, err := pm.config.GetConfigForEnvironment(env)
	if err != nil {
		return nil, err
	}

	if matchmakerConfigName == "" {
		matchmakerConfigName = defaultMatchmakerConfig
	}

	matchmakerConfig, found := playlistConfig.MatchmakerConfigs[matchmakerConfigName]
	if !found {
		return nil, fmt.Errorf("matchmaker configuration not found: %s", matchmakerConfigName)
	}

	return &matchmakerConfig, nil
}

func (pm *PlaylistManager) calculatePlaylistWeights(newPlaylists map[string]Playlist) {
	for playlistName := range newPlaylists {
		var totalWeight float64 = 0
		for _, mapMode := range newPlaylists[playlistName].MapModeList {
			totalWeight += float64(mapMode.Weight)
		}

		var cumulativeWeight float64 = 0
		for i := 0; i < len(newPlaylists[playlistName].MapModeList)-1; i++ {
			weight := float64(newPlaylists[playlistName].MapModeList[i].Weight) / totalWeight
			newPlaylists[playlistName].MapModeList[i].CalculatedWeight = weight + cumulativeWeight
			cumulativeWeight += weight
		}
		newPlaylists[playlistName].MapModeList[len(newPlaylists[playlistName].MapModeList)-1].CalculatedWeight = 1
	}
}
