package matchstart

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/fleetmanager"
	"wildlight.gg/bluejay/matchmaking"
	"wildlight.gg/bluejay/playlists"
	"wildlight.gg/bluejay/utils"
)

const envProperty = "env"

type LaunchMatch struct{}

type LaunchMatchState struct {
	presences        map[string]runtime.Presence
	emptyTicks       int
	launchToken      string
	serverInstanceId string
	serverAllocated  bool
	launchInfo       string
	launchTokenSent  bool
	matchStarted     bool
}

type launchInfo struct {
	Id    string `json:"id"`
	Ipv4  string `json:"ipv4"`
	Ipv6  string `json:"ipv6"`
	Port  int    `json:"port"`
	Token string `json:"token"`
}

type matchPlayer struct {
	UserId               string
	DisplayName          string
	Platform             string
	TeamId               int
	JwtToken             string
}

type serverToken struct {
	MapName      string
	ModeName     string
	Environment  string
	PlaylistName string
	Players      []matchPlayer
}

type createInstanceResponse struct {
	Err          string
	InstanceInfo runtime.InstanceInfo
}

func (m *LaunchMatch) MatchInit(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, params map[string]interface{}) (interface{}, int, string) {
	launchToken, ok := params[fleetmanager.MetadataLaunchToken].(string)
	if !ok {
		logger.Error("Match is missing launch token")
		return nil, 0, ""
	}

	state := &LaunchMatchState{
		emptyTicks:  0,
		presences:   map[string]runtime.Presence{},
		launchToken: launchToken,
	}

	tickRate := 1 // 1 tick per second = 1 MatchLoop func invocations per second
	label := ""

	logger.Info("Match starting, params: %v", params)

	return state, tickRate, label
}

func (m *LaunchMatch) MatchJoin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) interface{} {
	launchState, ok := state.(*LaunchMatchState)
	if !ok {
		logger.Error("state not a valid launch state object")
		return nil
	}

	for i := 0; i < len(presences); i++ {
		launchState.presences[presences[i].GetSessionId()] = presences[i]
	}

	if launchState.serverAllocated {
		err := dispatcher.BroadcastMessage(420, []byte(launchState.launchInfo), presences, nil, true)
		if err != nil {
			logger.Error("error sending launch info: %v", err)
		}
	} else {
		err := dispatcher.BroadcastMessage(419, []byte("allocating"), presences, nil, true)
		if err != nil {
			logger.Error("error sending launch status: %v", err)
		}
	}

	launchState.matchStarted = true
	return launchState
}

func (m *LaunchMatch) MatchLeave(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presences []runtime.Presence) interface{} {
	launchState, ok := state.(*LaunchMatchState)
	if !ok {
		logger.Error("state not a valid launch state object")
		return nil
	}

	for i := 0; i < len(presences); i++ {
		delete(launchState.presences, presences[i].GetSessionId())
	}

	return launchState
}

func (m *LaunchMatch) MatchLoop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, messages []runtime.MatchData) interface{} {
	launchState, ok := state.(*LaunchMatchState)
	if !ok {
		logger.Error("state not a valid lobby state object")
		return nil
	}

	presences := []runtime.Presence{}
	for _, p := range launchState.presences {
		presences = append(presences, p)
	}

	if !launchState.launchTokenSent && launchState.serverAllocated {
		err := dispatcher.BroadcastMessage(420, []byte(launchState.launchInfo), presences, nil, true)
		if err != nil {
			logger.Error("Error dispatching message: %v", err)
			return nil
		} else {
			logger.Info("Sending launch token: token=%s", launchState.launchInfo)
			launchState.launchTokenSent = true
		}
	} else if !launchState.serverAllocated {
		err := dispatcher.BroadcastMessage(419, []byte("allocating"), presences, nil, true)
		if err != nil {
			logger.Error("error sending launch status: %v", err)
		}
	}

	// If we have no presences in the match according to the match state, increment the empty ticks count
	if len(launchState.presences) == 0 {
		launchState.emptyTicks++
	}

	// If the match is empty, end the match by returning nil
	if (launchState.matchStarted && launchState.emptyTicks > 5) ||
		(!launchState.matchStarted && launchState.emptyTicks > 60) {
		return nil
	}

	return launchState
}

func (m *LaunchMatch) MatchJoinAttempt(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, presence runtime.Presence, metadata map[string]string) (interface{}, bool, string) {
	return state, true, ""
}

func (m *LaunchMatch) MatchTerminate(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, graceSeconds int) interface{} {
	return nil
}

func (m *LaunchMatch) NotifyMatchEnd(ctx context.Context, logger runtime.Logger, dispatcher runtime.MatchDispatcher, launchState *LaunchMatchState) {
	presences := []runtime.Presence{}
	for _, p := range launchState.presences {
		presences = append(presences, p)
	}

	err := dispatcher.BroadcastMessage(255, nil, presences, nil, true)
	if err != nil {
		logger.Error("Error dispatching message: %v", err)
	} else {
		logger.Info("Sending match termination signal")
	}
}

func (m *LaunchMatch) MatchSignal(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, dispatcher runtime.MatchDispatcher, tick int64, state interface{}, data string) (interface{}, string) {
	launchState, ok := state.(*LaunchMatchState)
	if !ok {
		logger.Error("state not a valid lobby state object")
		return nil, ""
	}

	createResponse := createInstanceResponse{}
	err := json.Unmarshal([]byte(data), &createResponse)

	if err != nil {
		logger.Error("Invalid payload received in match signal: %v", err)
		m.NotifyMatchEnd(ctx, logger, dispatcher, launchState)
		return nil, ""
	}

	if createResponse.Err != "" {
		logger.Error("Error returned from fleet manager: %v", createResponse.Err)
		m.NotifyMatchEnd(ctx, logger, dispatcher, launchState)
		return nil, ""
	}

	info := launchInfo{
		Id:    createResponse.InstanceInfo.Id,
		Ipv4:  createResponse.InstanceInfo.ConnectionInfo.IpAddress,
		Ipv6:  createResponse.InstanceInfo.ConnectionInfo.DnsName, // hax
		Port:  createResponse.InstanceInfo.ConnectionInfo.Port,
		Token: launchState.launchToken,
	}

	jsonInfo, err := json.Marshal(info)
	if err != nil {
		logger.Error("Error dispatching message: %v", err)
		m.NotifyMatchEnd(ctx, logger, dispatcher, launchState)
		return nil, ""
	}

	launchState.serverInstanceId = createResponse.InstanceInfo.Id
	launchState.launchInfo = string(jsonInfo)
	launchState.serverAllocated = true

	return launchState, ""
}

type MatchLauncher struct {
	playlistManager *playlists.PlaylistManager
}

func NewMatchLauncher(playlistManager *playlists.PlaylistManager) *MatchLauncher {
	return &MatchLauncher{
		playlistManager: playlistManager,
	}
}

func (m *MatchLauncher) makeServerLaunchToken(ctx context.Context, nk runtime.NakamaModule, logger runtime.Logger, entries []runtime.MatchmakerEntry, mapName string, modeName string, environment string, playlistName string) (string, error) {
	token := serverToken{
		MapName:      mapName,
		ModeName:     modeName,
		Environment:  environment,
		PlaylistName: playlistName,
	}

	token.Players = make([]matchPlayer, len(entries))

	for i, entry := range entries {
		team, found := entry.GetProperties()[matchmaking.TeamNumberProperty]
		if !found {
			logger.WithField("userId", entry.GetPresence().GetUserId()).Error("No team number specified for player")
			return "", nil
		}

		teamNum, ok := team.(int)
		if !ok {
			logger.WithField("userId", entry.GetPresence().GetUserId()).WithField("team", team).Error("invalid team number")
			return "", nil
		}

		platform, found := entry.GetProperties()[entry.GetPresence().GetUserId()+"_platform"]
		if !found {
			platform = "unknown"
		}

		var jwtToken string
		if utils.SatoriTokenGenerator != nil {
			var err error
			jwtToken, err = utils.SatoriTokenGenerator.MakeJwtTokenForPlayer(ctx, entry.GetPresence().GetUserId())
			if err != nil {
				logger.WithField("userId", entry.GetPresence().GetUserId()).WithField("err", err).Error("error generating Satori jwt token")
				return "", nil
			}
		}

		// Temp disabled 
		// fullStatsAndChallengeInfo, err := progression.LoadStatsAndChallengesForUserID(ctx, nk, entry.GetPresence().GetUserId(), logger)

		// if err != nil {
		// 	logger.WithField("userId", entry.GetPresence().GetUserId()).WithField("err", err).Error("error loading users progression information")

		// }

		// fullUserLoadout, _, err := entitlements.ReadUserLoadout(ctx, nk, entry.GetPresence().GetUserId(), logger)

		// if err != nil {
		// 	logger.WithField("userId", entry.GetPresence().GetUserId()).WithField("err", err).Error("error loading users equipement information")

		// }

		token.Players[i] = matchPlayer{
			UserId:               entry.GetPresence().GetUserId(),
			DisplayName:          entry.GetPresence().GetUsername(),
			Platform:             platform.(string),
			TeamId:               teamNum,
			JwtToken:             jwtToken,
		}
	}

	logger.WithField("token", token).Info("Server launch token generated")

	jsonOut, err := json.Marshal(token)
	if err != nil {
		logger.WithField("err", err).Error("Error writing json output")
		return "", err
	}

	var buffer bytes.Buffer
	gzipper, _ := gzip.NewWriterLevel(&buffer, gzip.BestCompression)
	_, err = gzipper.Write(jsonOut)
	if err != nil {
		logger.WithField("err", err).Error("Error writing to gzip string")
		return "", err
	}

	err = gzipper.Close()
	if err != nil {
		logger.WithField("err", err).Error("Error closing gzip string")
		return "", err
	}

	signer := hmac.New(sha1.New, []byte("supersecret"))
	base64Json := fmt.Sprintf("%x.%s", len(jsonOut), base64.StdEncoding.EncodeToString(buffer.Bytes()))
	signer.Write([]byte(base64Json))
	signature := base64.StdEncoding.EncodeToString(signer.Sum(nil))

	return fmt.Sprintf("%s.%s", signature, base64Json), nil
}

func (m *MatchLauncher) MatchmakerMatched(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, entries []runtime.MatchmakerEntry) (string, error) {
	if len(entries) > 0 {
		properties := entries[0].GetProperties()
		if properties["matchType"] == "game" {

			playlistId, found := properties["playlist"]
			if !found {
				return "", errors.New("must specify a playlist")
			}

			envName, found := properties[envProperty]
			if !found {
				envName = "default"
			}

			buildVersion, found := properties["build"]
			if !found {
				return "", errors.New("must specify a build")
			}

			matchId, found := properties["matchId"]
			if !found {
				matchId = uuid.Must(uuid.NewRandom()).String()
			}

			mapName, found := properties["map"]
			if !found {
				return "", errors.New("must specify a map")
			}

			modeName, found := properties["mode"]
			if !found {
				return "", errors.New("must specify a mode")
			}

			launchToken, err := m.makeServerLaunchToken(ctx, nk, logger, entries, mapName.(string), modeName.(string), envName.(string), playlistId.(string))
			if err != nil {
				return "", err
			}

			matchMetadata := map[string]interface{}{
				"debug":                           true,
				fleetmanager.MetadataMatchId:      matchId,
				fleetmanager.MetadataMapName:      mapName.(string),
				fleetmanager.MetadataModeName:     modeName.(string),
				fleetmanager.MetadataBuildVersion: buildVersion.(string),
				fleetmanager.MetadataLaunchToken:  launchToken,
			}

			region, found := properties["region"]
			if found {
				matchMetadata[fleetmanager.MetadataRegion] = region.(string)
			} else {
				region = ""
			}

			nakamaMatchId, err := nk.MatchCreate(ctx, "launch", matchMetadata)
			if err != nil {
				return "", err
			}

			logger.WithField("nakamaMatchId", nakamaMatchId).WithField("matchId", matchId).Info("Starting new nakama match to wait for server provisioning")

			fleetManager := nk.GetFleetManager()
			if fleetManager == nil {
				logger.Error("Fleet manager is nil")
				return "", errors.New("could not access fleetmanager")
			}

			var callback runtime.FmCreateCallbackFn = func(status runtime.FmCreateStatus, instanceInfo *runtime.InstanceInfo, sessionInfo []*runtime.SessionInfo, metadata map[string]any, createErr error) {

				response := createInstanceResponse{}

				switch status {
				case runtime.CreateSuccess:
					logger.Info("Fleet instance [%s] created: %s:%d", instanceInfo.Id, instanceInfo.ConnectionInfo.IpAddress, instanceInfo.ConnectionInfo.Port)
					response.InstanceInfo = *instanceInfo
				case runtime.CreateTimeout:
					logger.Error("Timeout creating fleet instance")
					response.Err = "timeout creating fleet instance"
				default:
					logger.Error("Failed to create fleet instance: %v", createErr)
					response.Err = createErr.Error()
				}

				responseJson, err := json.Marshal(response)
				if err != nil {
					logger.Error("Error creating response: %v", err)
				}

				nk.MatchSignal(ctx, nakamaMatchId, string(responseJson))
			}

			maxPlayers := 18
			metadata := map[string]any{
				fleetmanager.MetadataMatchId:      matchId,
				fleetmanager.MetadataMapName:      mapName.(string),
				fleetmanager.MetadataModeName:     modeName.(string),
				fleetmanager.MetadataBuildVersion: buildVersion.(string),
				fleetmanager.MetadataLaunchToken:  launchToken,
			}

			if region != "" {
				metadata[fleetmanager.MetadataRegion] = region
			}

			playerIds := []string{}
			latencies := []runtime.FleetUserLatencies{}

			for _, player := range entries {
				userId := player.GetPresence().GetUserId()
				playerIds = append(playerIds, userId)

				for k, v := range player.GetProperties() {
					k, found = strings.CutPrefix(k, "R_")
					if found {
						tokens := strings.Split(k, "_")
						if len(tokens) == 2 && tokens[1] == userId {
							ping, ok := v.(float64)
							if ok {
								latencies = append(latencies,
									runtime.FleetUserLatencies{
										UserId:                userId,
										RegionIdentifier:      tokens[0],
										LatencyInMilliseconds: float32(ping),
									})

								logger.WithField("region", tokens[0]).WithField("ping", v).WithField("userId", tokens[1]).Info("Pingy")
							}
						}
					}
				}
			}

			err = fleetManager.Create(ctx, maxPlayers, playerIds, latencies, metadata, callback)
			if err != nil {
				logger.Error("Error creating fleet instance: %v", err)
				return "", err
			}

			return nakamaMatchId, nil
		}
	}

	return "", nil
}
