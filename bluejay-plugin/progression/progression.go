package progression

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/entitlements"
	"wildlight.gg/bluejay/errors"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

func ApplyStatsAndChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))

	if err != nil {
		logger.WithField("error", err).Error("Got error loading config")
		return "", err
	}

	request := MatchInfoPayload{AllStats: []CompositeStat{}, MatchStats: []CompositeMatchStat{}, Challenges: map[string]ChallengeProgress{}, ChallengesPreUpdate: map[string]ChallengeProgress{}, GameModeString: "", MapName: "", MatchResult: -1, PlayerID: ""}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if len(request.PlayerID) == 0 {
		return "", errors.NoUserIdFound
	}

	logger.Info("Got request: %v from payload %s", request, payload)

	statWriteRequests := []*runtime.StorageWrite{}

	userStats, version, err := readStats(ctx, nk, request.PlayerID)

	if err != nil {
		return "", err
	}

	if userStats.Stats == nil {
		userStats.Stats = map[string]CompositeStat{}
	}

	for _, stat := range request.AllStats {
		userStats.Stats[stat.Name] = stat
	}

	if statBytes, err := json.Marshal(userStats); err != nil {
		return "", err
	} else {
		writeRequest := runtime.StorageWrite{
			Collection: storage.PROGRESSION_STATS_COLLECTION,
			Key:        storage.PROGRESSION_STATS,
			UserID:     request.PlayerID,
			Value:      string(statBytes),
			Version:    version,
		}

		logger.Info("AllStats storage write %v ", writeRequest)

		statWriteRequests = append(statWriteRequests, &writeRequest)
	}

	if value, err := json.Marshal(PreviousMatchStorageWrapper{LastMatchStats: request.MatchStats, ChallengesPreUpdate: request.ChallengesPreUpdate, GameModeString: request.GameModeString, MapName: request.MapName, MatchResult: request.MatchResult}); err != nil {
		return "", err
	} else {

		writeRequest := runtime.StorageWrite{
			Collection:      storage.PROGRESSION_MATCH_COLLECTION,
			Key:             storage.USER_PREVIOUS_MATCH,
			UserID:          request.PlayerID,
			Value:           string(value),
			PermissionRead:  1,
			PermissionWrite: 1,
		}

		logger.Info("MatchStats storage write %v ", writeRequest)

		statWriteRequests = append(statWriteRequests, &writeRequest)
	}

	if len(request.Challenges) > 0 {
		activeChallenges, activeVersion, err := loadActiveChallenges(ctx, nk, request.PlayerID)
		if err != nil {
			return "", err
		}

		challengeEvents := []any{}

		for poolName, challenge := range activeChallenges.Challenges {
			info, exists := request.Challenges[challenge.Name]
			if exists {
				if info.Completed && info.Completed != challenge.Completed {
					challenge.Completed = info.Completed

					assetName := challengesConfig.AssetNames[challenge.Name]

					challengeEvent := utils.ChallengeEvent{MaxProgress: int(info.MaxProgress), ID: challenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_COMPLETED, CurrentProgress: int(challenge.Progress), Completed: challenge.Completed}

					challengeEvents = append(challengeEvents, challengeEvent)
				}

				if info.Progress != challenge.Progress {
					assetName := challengesConfig.AssetNames[challenge.Name]

					challengeEvent := utils.ChallengeEvent{MaxProgress: int(info.MaxProgress), ID: challenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_PROGRESSED, CurrentProgress: int(challenge.Progress), Completed: challenge.Completed}

					challengeEvents = append(challengeEvents, challengeEvent)
					challenge.Progress = info.Progress
				}

				activeChallenges.Challenges[poolName] = challenge
			}

		}

		if len(challengeEvents) != 0 {
			utils.PublishMultipleSatoriEvents(ctx, logger, CHALLENGE_UPDATE_EVENT, challengeEvents)
		}

		if challengesBytes, err := json.Marshal(activeChallenges); err != nil {
			return "", err
		} else {

			statWriteRequests = append(statWriteRequests, &runtime.StorageWrite{
				Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
				Key:        storage.ACTIVE_CHALLENGES,
				UserID:     request.PlayerID,
				Value:      string(challengesBytes),
				Version:    activeVersion,
			})

		}
	}

	if walletUpdates, storageWriteUpdates, err := createRewardUpdates(ctx, nk, request.Rewards, request.PlayerID, logger); err != nil {
		return "", err
	} else {

		*storageWriteUpdates = append(*storageWriteUpdates, statWriteRequests...)

		logger.Info("storage and wallet writes %v and %v", *storageWriteUpdates, *walletUpdates)

		// Capture wallet state BEFORE the transaction
		previousWalletState, err := getPreviousWalletState(ctx, nk, request.PlayerID, logger)
		if err != nil {
			return "", err
		}

		if storageAcks, walletUpdateResults, err := nk.MultiUpdate(ctx, []*runtime.AccountUpdate{}, *storageWriteUpdates, []*runtime.StorageDelete{}, *walletUpdates, true); err != nil {
			logger.WithField("err", err).Error("Multi update error.")
			return "", err
		} else {
			logger.Info("Storage Acks: %d", len(storageAcks))
			logger.Info("Wallet Updates: %d", len(walletUpdateResults))

			if updateJSON, err := json.Marshal(request.Rewards); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				// send notification
				if err := nk.NotificationSend(ctx, request.PlayerID, "matchRewards", map[string]interface{}{"Rewards": updateJSON}, utils.LAST_MATCH_REWARDS, "", false); err != nil {
					return "", err
				} else {

					err := publishRewardEventsForSatori(ctx, nk, request, logger, previousWalletState)
					if err != nil {
						return "", err
					}

					jsonString := string(updateJSON)
					return jsonString, nil
				}

			}
		}
	}
}

func getPreviousWalletState(ctx context.Context, nk runtime.NakamaModule, userID string, logger runtime.Logger) (map[string]int64, error) {
	acct, err := nk.AccountGetId(ctx, userID)
	if err != nil {
		logger.WithField("error", err).Error("Error getting user account info for previous wallet state")
		return nil, err
	}

	wallet := map[string]int64{}
	err = json.Unmarshal([]byte(acct.Wallet), &wallet)
	if err != nil {
		logger.WithField("error", err).WithField("wallet", acct.Wallet).Error("JSON parse error for previous wallet state")
		return nil, err
	}

	return wallet, nil
}

func publishRewardEventsForSatori(ctx context.Context, nk runtime.NakamaModule, request MatchInfoPayload, logger runtime.Logger, previousWalletState map[string]int64) error {
	txID, err := uuid.NewV7()

	if err != nil {
		logger.WithField("error", err).Error("Unable to generate transaction ID")
		return err
	}

	walletRewardEvents := []any{}

	// Get updated wallet state AFTER the transaction
	acct, err := nk.AccountGetId(ctx, request.PlayerID)
	if err != nil {
		logger.WithField("error", err).Error("Error getting user account info")
		return err
	}

	updatedWallet := map[string]int64{}
	err = json.Unmarshal([]byte(acct.Wallet), &updatedWallet)
	if err != nil {
		logger.WithField("error", err).WithField("wallet", acct.Wallet).Error("JSON parse error")
		return err
	}

	for currency := range request.Rewards.CurrencyRewards {

		// Get previous balance from the captured state
		previousBalance, exists := previousWalletState[currency]
		if !exists {
			previousBalance = 0
		}

		// Get updated balance from current wallet state
		updatedBalance, exists := updatedWallet[currency]
		if !exists {
			updatedBalance = 0
		}

		// Determine transaction type based on actual balance change
		transactionType := "source" // Default to source
		if updatedBalance < previousBalance {
			transactionType = "sink" // Balance decreased = money going out
		} else if updatedBalance > previousBalance {
			transactionType = "source" // Balance increased = money coming in
		}

		// Calculate the actual delta
		actualDelta := updatedBalance - previousBalance

		event := utils.WalletEvent{
			TransactionID:   txID.String(),
			TransactionType: transactionType,
			Reason:          MATCH_REWARD,
			CurrencyType:    currency,
			CurrencyAmount:  int(actualDelta),     // Actual delta (negative for spending, positive for gaining)
			PreviousBalance: int(previousBalance), // Balance before reward (explicit)
			CurrencyBalance: int(updatedBalance),  // Balance after reward (backward compatibility)
			UpdatedBalance:  int(updatedBalance),  // Balance after reward (explicit)
		}

		walletRewardEvents = append(walletRewardEvents, event)
	}

	utils.PublishMultipleSatoriEvents(ctx, logger, WALLET_UPDATE_EVENT, walletRewardEvents)

	entitlementRewardEvents := []any{}

	for _, entitlement := range request.Rewards.EntitlementRewards {

		event := utils.EntitlementEvent{TransactionID: txID.String(), ProductID: entitlement, Source: MATCH_REWARD}

		entitlementRewardEvents = append(entitlementRewardEvents, event)
	}

	utils.PublishMultipleSatoriEvents(ctx, logger, ENTITLEMENT_UPDATE_EVENT, entitlementRewardEvents)
	return nil
}

func LoadStatsAndChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	var userID string

	if len(payload) != 0 {

		request := entitlements.UserIDRequest{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}

		userID = request.ID

	}

	if len(userID) == 0 {
		var ok bool
		userID, ok = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

		if !ok {
			// User id must exist in the context for this RPC
			logger.Error("No userId found")
		}
	}

	if len(userID) == 0 {
		logger.Error("No user id could be extracted from payload %s, or context", payload)
		return "", errors.NoUserIdFound
	}

	fullInfo, err := LoadStatsAndChallengesForUserID(ctx, nk, userID, logger)
	if err != nil {
		return "", err
	}

	if value, err := json.Marshal(fullInfo); err != nil {
		return "", err
	} else {
		return string(value), nil
	}

}

func ResetPlayerStats(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	var userID string
	var ok bool

	userID, ok = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	if !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	deleteStatRequest := runtime.StorageDelete{
		Collection: storage.PROGRESSION_STATS_COLLECTION,
		Key:        storage.PROGRESSION_STATS,
		UserID:     userID,
	}

	if err := nk.StorageDelete(ctx, []*runtime.StorageDelete{&deleteStatRequest}); err != nil {
		return "", err
	} else {
		return "", nil
	}

}

func ResetPlayerChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	var userID string
	var ok bool

	userID, ok = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	if !ok {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	deleteStatRequest := runtime.StorageDelete{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.ACTIVE_CHALLENGES,
		UserID:     userID,
	}

	if err := nk.StorageDelete(ctx, []*runtime.StorageDelete{&deleteStatRequest}); err != nil {
		return "", err
	} else {
		return "", nil
	}
}

func ResetPlayerStatsAndChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	userID := utils.GetUserId(ctx)

	if len(userID) == 0 {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	deleteRequests := []*runtime.StorageDelete{}

	deleteStatRequest := runtime.StorageDelete{
		Collection: storage.PROGRESSION_STATS_COLLECTION,
		Key:        storage.PROGRESSION_STATS,
		UserID:     userID,
	}

	deleteRequests = append(deleteRequests, &deleteStatRequest)

	deleteLastMatchStatsRequest := runtime.StorageDelete{
		Collection: storage.PROGRESSION_MATCH_COLLECTION,
		Key:        storage.USER_LAST_MATCH,
		UserID:     userID,
	}

	deleteRequests = append(deleteRequests, &deleteLastMatchStatsRequest)

	deleteActiveChallenges := runtime.StorageDelete{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.ACTIVE_CHALLENGES,
		UserID:     userID,
	}
	deleteRequests = append(deleteRequests, &deleteActiveChallenges)

	deleteChallengeHistoryRequest := runtime.StorageDelete{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.CHALLENGE_HISTORY,
		UserID:     userID,
	}
	deleteRequests = append(deleteRequests, &deleteChallengeHistoryRequest)

	if err := nk.StorageDelete(ctx, deleteRequests); err != nil {
		return "", err
	} else {
		return "", nil
	}

}

func CompleteChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := utils.GetUserId(ctx)

	if len(userID) == 0 {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	}

	request := CompleteChallengeRequest{}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if len(request.Challenges) == 0 {
		return "", errors.InvalidPayload
	}

	activeChallenges, version, _ := loadActiveChallenges(ctx, nk, userID)

	challengesToMarkComplete := map[string]bool{}

	for _, challenge := range request.Challenges {
		challengesToMarkComplete[challenge] = true
	}

	for slot, activeChallenge := range activeChallenges.Challenges {
		if _, exists := challengesToMarkComplete[activeChallenge.Name]; exists {
			activeChallenge.Completed = true
			activeChallenges.Challenges[slot] = activeChallenge
		}
	}

	err := saveUpdatedChallengeData(ctx, nk, userID, activeChallenges, version, ChallengeHistory{}, "")
	return "", err
}

func getChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, userID string) (ActiveChallenges, map[string]SlotInformation, error) {
	if len(userID) == 0 {
		userID = utils.GetUserId(ctx)
	}

	if len(userID) == 0 {
		// User id must exist in the context for this RPC
		logger.Error("No userId found")
		return ActiveChallenges{}, map[string]SlotInformation{}, errors.NoUserIdFound
	}

	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))
	if err != nil {
		logger.Error("Error loading configuration.")
		return ActiveChallenges{}, map[string]SlotInformation{}, err
	}

	serverTime := time.Now()

	activeChallenges, activeVersion, err := loadActiveChallenges(ctx, nk, userID)

	logger.WithField("numChallenges", len(activeChallenges.Challenges)).Info("Found challenges.")

	if err != nil {
		logger.WithField("user", userID).Error("Error reading active challenges for user.")
		return ActiveChallenges{}, map[string]SlotInformation{}, err
	}

	challengeHistory, historyVersion, err := loadChallengeHistory(ctx, nk, userID)

	logger.WithField("history", challengeHistory).Info("Challenge  history.")

	if err != nil {
		logger.WithField("user", userID).Error("Error reading challengeHistory for user.")
		return ActiveChallenges{}, map[string]SlotInformation{}, err
	}

	// Track which challenges per pool are assigned
	assignments := initPoolAssignments(activeChallenges, challengesConfig.Slots, logger)

	logger.WithField("assignements", assignments).Info("Currently assigned challenges.")

	challengeEvents := []any{}

	// Remove expired challenges
	processExpiredChallenges(ctx, challengesConfig.Slots, challengesConfig.Pools, activeChallenges, assignments, serverTime, logger, &challengeEvents)

	updated, err := assignNewChallenge(ctx, challengesConfig.Slots, challengesConfig.Pools, activeChallenges, challengeHistory, assignments, serverTime, logger, &challengeEvents)

	if err != nil {
		logger.WithField("user", userID).Error("Assigning new challenge failed.")
		return ActiveChallenges{}, map[string]SlotInformation{}, err
	}

	// Save off our updated challenges if any were modified
	if updated {
		err = saveUpdatedChallengeData(ctx, nk, userID, activeChallenges, activeVersion, challengeHistory, historyVersion)
		if err != nil {
			return ActiveChallenges{}, map[string]SlotInformation{}, err
		}
	}

	// poolname -> slotname -> challengename
	fullPoolSlotInfo := map[string]SlotInformation{}

	for slotName, slotChallenge := range activeChallenges.Challenges {
		for poolName := range assignments {
			_, challengeExists := assignments[poolName][slotChallenge.Name]

			if challengeExists {
				slotInfo, infoExists := fullPoolSlotInfo[poolName]

				if !infoExists {
					slotInfo = SlotInformation{SlotInformation: map[string]string{}}
				}

				slotInfo.SlotInformation[slotName] = slotChallenge.Name
				fullPoolSlotInfo[poolName] = slotInfo
			}
		}
	}

	utils.PublishMultipleSatoriEvents(ctx, logger, CHALLENGE_UPDATE_EVENT, challengeEvents)

	return activeChallenges, fullPoolSlotInfo, nil

}

func SetPlayerStatsAndChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	request := FullMatchStatsAndRewardsPayload{AllStats: []CompositeStat{}, MatchStats: []CompositeMatchStat{}, Challenges: []ChallegeGroupProgressInfo{}, ChallengesPreUpdate: []ChallegeGroupProgressInfo{}, GameModeString: "", MapName: "", MatchResult: -1, PlayerID: ""}

	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
		return "", err
	}

	if len(request.PlayerID) == 0 {
		return "", errors.NoUserIdFound
	}

	logger.Info("Got request: %v from payload %s", request, payload)

	statWriteRequests := []*runtime.StorageWrite{}

	userStats, version, err := readStats(ctx, nk, request.PlayerID)

	if err != nil {
		return "", err
	}

	if userStats.Stats == nil {
		userStats.Stats = map[string]CompositeStat{}
	}

	for _, stat := range request.AllStats {
		userStats.Stats[stat.Name] = stat
	}

	if statBytes, err := json.Marshal(userStats); err != nil {
		return "", err
	} else {
		writeRequest := runtime.StorageWrite{
			Collection: storage.PROGRESSION_STATS_COLLECTION,
			Key:        storage.PROGRESSION_STATS,
			UserID:     request.PlayerID,
			Value:      string(statBytes),
			Version:    version,
		}

		logger.Info("AllStats storage write %v ", writeRequest)

		statWriteRequests = append(statWriteRequests, &writeRequest)
	}

	if value, err := json.Marshal(LastMatchStorageWrapper{LastMatchStats: request.MatchStats, ChallengesPreUpdate: request.ChallengesPreUpdate, GameModeString: request.GameModeString, MapName: request.MapName, MatchResult: request.MatchResult}); err != nil {
		return "", err
	} else {

		writeRequest := runtime.StorageWrite{
			Collection:      storage.PROGRESSION_MATCH_COLLECTION,
			Key:             storage.USER_LAST_MATCH,
			UserID:          request.PlayerID,
			Value:           string(value),
			PermissionRead:  1,
			PermissionWrite: 1,
		}

		logger.Info("MatchStats storage write %v ", writeRequest)

		statWriteRequests = append(statWriteRequests, &writeRequest)
	}

	userChallenges, version, err := readChallenges(ctx, nk, request.PlayerID)

	if err != nil {
		return "", err
	}

	if userChallenges.Challenges == nil {
		userChallenges.Challenges = map[string]ChallegeGroupProgressInfo{}
	}

	for _, challengeGroupInfo := range request.Challenges {
		userChallenges.Challenges[challengeGroupInfo.ChallengeID] = challengeGroupInfo
	}

	if challengesBytes, err := json.Marshal(userChallenges); err != nil {
		return "", err
	} else {

		writeRequest := runtime.StorageWrite{
			Collection:      storage.PROGRESSION_CHALLENGES_COLLECTION,
			Key:             storage.PROGRESSION_CHALLENGES,
			UserID:          request.PlayerID,
			Value:           string(challengesBytes),
			PermissionRead:  1,
			PermissionWrite: 1,
			Version:         version,
		}

		logger.Info("Challenges storage write %v ", writeRequest)

		statWriteRequests = append(statWriteRequests, &writeRequest)
	}

	if walletUpdates, storageWriteUpdates, err := createRewardUpdates(ctx, nk, request.Rewards, request.PlayerID, logger); err != nil {
		return "", err
	} else {

		*storageWriteUpdates = append(*storageWriteUpdates, statWriteRequests...)

		logger.Info("storage and wallet writes %v and %v", *storageWriteUpdates, *walletUpdates)

		if storageAcks, walletUpdateResults, err := nk.MultiUpdate(ctx, []*runtime.AccountUpdate{}, *storageWriteUpdates, []*runtime.StorageDelete{}, *walletUpdates, true); err != nil {
			logger.WithField("err", err).Error("Multi update error.")
			return "", err
		} else {
			logger.Info("Storage Acks: %d", len(storageAcks))
			logger.Info("Wallet Updates: %d", len(walletUpdateResults))

			if updateJSON, err := json.Marshal(request.Rewards); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				// send notification
				if err := nk.NotificationSend(ctx, request.PlayerID, "matchRewards", map[string]interface{}{"Rewards": updateJSON}, utils.LAST_MATCH_REWARDS, "", false); err != nil {
					return "", err
				} else {
					jsonString := string(updateJSON)
					return jsonString, nil
				}

			}
		}
	}
}

func GetPlayerStatsAndChallenges(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	var userID string

	if len(payload) != 0 {

		request := entitlements.UserIDRequest{}

		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", err
		}

		userID = request.ID
	} else {
		var ok bool
		userID, ok = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

		if !ok {
			// User id must exist in the context for this RPC
			logger.Error("No userId found")
		}
	}

	if len(userID) == 0 {
		logger.Error("No user id could be extracted from payload %s, or context", payload)
		return "", errors.NoUserIdFound
	}

	if userStats, _, err := readStats(ctx, nk, userID); err != nil {
		return "", err
	} else {
		readRequest := runtime.StorageRead{
			Collection: storage.PROGRESSION_MATCH_COLLECTION,
			Key:        storage.USER_LAST_MATCH,
			UserID:     userID,
		}

		readRequests := []*runtime.StorageRead{&readRequest}

		if lastMatchObject, err := nk.StorageRead(ctx, readRequests); err != nil {
			return "", err
		} else {
			var lastMatchStats LastMatchStorageWrapper = LastMatchStorageWrapper{LastMatchStats: []CompositeMatchStat{}, ChallengesPreUpdate: []ChallegeGroupProgressInfo{}, GameModeString: "", MapName: "", MatchResult: -1}

			if len(lastMatchObject) != 0 {
				jsonStat := lastMatchObject[0]
				if err := json.Unmarshal([]byte(jsonStat.Value), &lastMatchStats); err != nil {
					return "", err
				}
			}

			if userChallenges, _, err := readChallenges(ctx, nk, userID); err != nil {
				return "", err
			} else {

				stats := []CompositeStat{}
				// convert stats into list for backwards compatability for now
				for _, stat := range userStats.Stats {
					stats = append(stats, stat)
				}

				challenges := []ChallegeGroupProgressInfo{}
				for _, challengeInfo := range userChallenges.Challenges {
					challenges = append(challenges, challengeInfo)
				}

				var fullInfo FullMatchStatsAndRewards = FullMatchStatsAndRewards{Stats: stats, LastMatchStats: lastMatchStats.LastMatchStats, Challenges: challenges, LastMatchChallengesPreUpdate: lastMatchStats.ChallengesPreUpdate, LastMatchMapName: lastMatchStats.MapName, LastMatchGameMode: lastMatchStats.GameModeString, LastMatchResult: lastMatchStats.MatchResult}

				if value, err := json.Marshal(fullInfo); err != nil {
					return "", err
				} else {

					return string(value), nil
				}
			}
		}
	}
}
