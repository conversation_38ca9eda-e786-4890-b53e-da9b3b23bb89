package auth

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/api"
	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/lestrrat-go/httprc/v3"
	"github.com/lestrrat-go/jwx/v3/jwk"
	"github.com/lestrrat-go/jwx/v3/jwt"
)

type PragmaAuth struct {
	keyCache      *jwk.Cache
	keySet        jwk.CachedSet
	allowFakeAuth bool
}

func RegisterPragmaAuth(ctx context.Context, initializer runtime.Initializer) error {
	auth := &PragmaAuth{}

	err := initializer.RegisterBeforeAuthenticateCustom(auth.BeforeAuthCustom)
	if err != nil {
		return err
	}

	auth.keyCache, err = jwk.NewCache(ctx, httprc.NewClient())
	if err != nil {
		return err
	}

	env, ok := ctx.Value(runtime.RUNTIME_CTX_ENV).(map[string]string)
	if !ok {
		return errors.New("expected RUNTIME_CTX_ENV to be a map[string]string")
	}

	allowFakeAuth, found := env["NC_ALLOW_FAKE_AUTH"]
	if found {
		auth.allowFakeAuth, err = strconv.ParseBool(allowFakeAuth)
		if err != nil {
			return fmt.Errorf("invalid value for NC_ALLOW_FAKE_AUTH: %s", allowFakeAuth)
		}
	}

	keyUrl, found := env["NC_PRAGMA_JWKS_ENDPOINT"]
	if !found {
		return errors.New("must specify NC_PRAGMA_JWKS_ENDPOINT")
	}

	err = auth.keyCache.Register(ctx, keyUrl, jwk.WithMinInterval(15*time.Minute), jwk.WithMaxInterval(24*time.Hour))
	if err != nil {
		return err
	}

	auth.keySet, err = auth.keyCache.CachedSet(keyUrl)
	if err != nil {
		return err
	}

	return nil
}

func (auth *PragmaAuth) BeforeAuthCustom(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.AuthenticateCustomRequest) (*api.AuthenticateCustomRequest, error) {
	if strings.Contains(in.Account.Id, ":") {
		if auth.allowFakeAuth {
			tokens := strings.SplitN(in.Account.Id, ":", 2)
			in.Account.Vars["displayName"] = tokens[1]
			in.Account.Vars["idProvider"] = "FAKE"
			return in, nil
		}

		return nil, errors.New("invalid authentication token")
	}

	token, err := jwt.Parse([]byte(in.Account.Id), jwt.WithKeySet(auth.keySet), jwt.WithAcceptableSkew(time.Second*10))
	if err != nil {
		slog.LogAttrs(ctx, slog.LevelError, "Error validating token", slog.Any("err", err), slog.String("jwt", in.Account.Id))
		return nil, err
	}

	var pragmaPlayerId string
	err = token.Get("pragmaPlayerId", &pragmaPlayerId)
	if err != nil {
		slog.ErrorContext(ctx, "pragmaPlayerId claim missing", slog.String("jwt", in.Account.Id))
		return nil, err
	}

	var displayName string
	err = token.Get("displayName", &displayName)
	if err != nil {
		slog.ErrorContext(ctx, "displayName claim missing", slog.String("jwt", in.Account.Id))
		return nil, err
	}

	var discriminator string
	err = token.Get("discriminator", &discriminator)
	if err != nil {
		slog.ErrorContext(ctx, "discriminator claim missing", slog.String("jwt", in.Account.Id))
		return nil, err
	}

	var idProvider string
	err = token.Get("idProvider", &idProvider)
	if err != nil {
		slog.ErrorContext(ctx, "idProvider claim missing", slog.String("jwt", in.Account.Id))
		return nil, err
	}

	switch idProvider {
	case "PLAYSTATION":
		in.Account.Vars["platform"] = "psn"
	case "XBOX":
		in.Account.Vars["platform"] = "xbox"
	case "STEAM":
		in.Account.Vars["platform"] = "steam"
	case "FAKE":
		in.Account.Vars["platform"] = "fake"
	}

	in.Account.Id = pragmaPlayerId
	in.Username = fmt.Sprintf("%s#%s", displayName, discriminator)

	return in, nil
}
