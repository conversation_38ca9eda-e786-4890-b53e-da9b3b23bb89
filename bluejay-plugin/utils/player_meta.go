package utils

import (
	"context"
	"encoding/json"

	"github.com/heroiclabs/nakama-common/runtime"
)

type PingRegion struct {
	Region string  `json:"region"`
	PingMs float64 `json:"pingMs"`
}

type PlayerMeta struct {
	Platform string       `json:"p"`
	Ping     []PingRegion `json:"r"`
}

type PlayerMetaSystem struct {
	nkRuntime runtime.NakamaModule
}

func NewPlayerMetaSystem(nkRuntime runtime.NakamaModule) *PlayerMetaSystem {
	return &PlayerMetaSystem{
		nkRuntime: nkRuntime,
	}
}

func (pm *PlayerMetaSystem) Get(logger runtime.Logger, userId string, sessionId string) (*PlayerMeta, error) {
	userMeta, err := pm.nkRuntime.StreamUserGet(1, userId, "", "", userId, sessionId)
	if err != nil {
		logger.WithField("err", err).Error("StreamUserGet failed")
		return nil, err
	}

	var playerStatus PlayerMeta

	if userMeta != nil && userMeta.GetStatus() != "" {
		err := json.Unmarshal([]byte(userMeta.GetStatus()), &playerStatus)
		if err != nil {
			logger.WithField("err", err).Error("error unmarshaling player status")
			return nil, err
		}
	}

	return &playerStatus, nil
}

func (pm *PlayerMetaSystem) GetFromContext(ctx context.Context, logger runtime.Logger) (*PlayerMeta, error) {
	userId := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	sessionId := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)

	return pm.Get(logger, userId, sessionId)
}

func (pm *PlayerMetaSystem) set(ctx context.Context, logger runtime.Logger, playerStatus *PlayerMeta) error {
	userId := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	sessionId := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID).(string)

	jsonStatus, err := json.Marshal(playerStatus)
	if err != nil {
		logger.WithField("err", err).Error("player status json marshal failed")
	}

	err = pm.nkRuntime.StreamUserUpdate(1, userId, "", "", userId, sessionId, false, false, string(jsonStatus))
	if err != nil {
		logger.WithField("err", err).Error("StreamUserUpdate failed")
		return err
	}

	return nil
}

func (pm *PlayerMetaSystem) InitializePlayer(ctx context.Context, logger runtime.Logger) error {
	meta := &PlayerMeta{
		Platform: GetPlayerPlatform(ctx),
	}

	err := pm.set(ctx, logger, meta)
	if err != nil {
		return err
	}

	return nil
}

func (pm *PlayerMetaSystem) Update(ctx context.Context, logger runtime.Logger, meta *PlayerMeta) error {
	err := pm.set(ctx, logger, meta)
	if err != nil {
		return err
	}

	return nil
}
