package utils

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/heroiclabs/nakama-common/runtime"
	wl_errors "wildlight.gg/bluejay/errors"
)

const defaultEnvironment = "default"
const defaultEnvironmentConsole = "bluejayfnf"

const defaultPlatform = "fake"

const defaultBuild = "unknown"

type NakamaRPC func(ctx context.Context, logger runtime.Logger, sql *sql.DB, nk runtime.NakamaModule, payload string) (string, error)

var UseSentry bool = false

var satori runtime.Satori

func getPlayerProperty(ctx context.Context, propertyName string, defaultValue string) string {
	propertyValue := ""
	vars := ctx.Value(runtime.RUNTIME_CTX_VARS)
	if vars != nil {
		varsMap, ok := vars.(map[string]string)
		if ok {
			propertyValue, ok = varsMap[propertyName]
			if !ok {
				propertyValue = defaultValue
			}
		}
	}

	if propertyValue == "" {
		return defaultValue
	}

	return propertyValue
}

func GetPlayerEnvironment(ctx context.Context) string {
	if GetPlayerPlatform(ctx) == "steam" {
		return getPlayerProperty(ctx, "beta", defaultEnvironment)
	}

	return getPlayerProperty(ctx, "beta", defaultEnvironmentConsole)
}

func GetPlayerPlatform(ctx context.Context) string {
	return getPlayerProperty(ctx, "platform", defaultPlatform)
}

func GetPlayerBuildVersion(ctx context.Context) string {
	return getPlayerProperty(ctx, "build", defaultBuild)
}

func GetUserId(ctx context.Context) string {
	val := ctx.Value(runtime.RUNTIME_CTX_USER_ID)
	s, ok := val.(string)
	if ok {
		return s
	}

	return ""
}

func GetSessionId(ctx context.Context) string {
	val := ctx.Value(runtime.RUNTIME_CTX_SESSION_ID)
	s, ok := val.(string)
	if ok {
		return s
	}

	return ""
}

func GetUserIpAddress(ctx context.Context) string {
	val := ctx.Value(runtime.RUNTIME_CTX_CLIENT_IP)
	s, ok := val.(string)
	if ok {
		return s
	}

	return ""
}

func executeWildlightRpc(ctx context.Context, logger runtime.Logger, sql *sql.DB, nk runtime.NakamaModule, payload string, rpcName string, rpc NakamaRPC) (out string, outError error) {
	logger = logger.WithField("rpc", rpcName)

	var span *sentry.Span
	defer func() {
		if span != nil {
			span.Finish()
		}
	}()

	if UseSentry {
		hub := sentry.GetHubFromContext(ctx)
		if hub == nil {
			hub = sentry.CurrentHub()
		}

		hub.Scope().SetUser(sentry.User{
			ID:        GetUserId(ctx),
			IPAddress: GetUserIpAddress(ctx),
			Data:      map[string]string{"platform": GetPlayerPlatform(ctx)},
		})

		logger = logger.WithField("traceid", hub.GetTraceparent())

		span = sentry.StartTransaction(ctx, rpcName, sentry.WithOpName("nakama"))
	}

	defer func() {
		err := recover()
		if err != nil {
			if UseSentry {
				hub := sentry.GetHubFromContext(ctx)
				if hub == nil {
					hub = sentry.CurrentHub()
				}

				hub.RecoverWithContext(ctx, err)
			}

			out = ""
			outError = errors.New("internal server error")

			logger.WithField("rpc", rpcName).WithField("panic", err).Error("Recovering from panic in rpc routine")
		}
	}()

	return rpc(ctx, logger, sql, nk, payload)
}

func RegisterWildlightRpc(initializer runtime.Initializer, logger runtime.Logger, rpcName string, rpc NakamaRPC) error {
	rpcFunc := func(ctx context.Context, logger runtime.Logger, sql *sql.DB, nk runtime.NakamaModule, payload string) (out string, outError error) {
		return executeWildlightRpc(ctx, logger, sql, nk, payload, rpcName, rpc)
	}

	err := initializer.RegisterRpc(rpcName, rpcFunc)
	if err != nil {
		logger.Error("unable to register %s: %v", rpcName, err)
	}
	return err
}

func BindSatoriClient(in runtime.Satori) error {
	if in == nil {
		return runtime.NewError("Unable to bind Satori client. Client is nil", 3)
	}

	satori = in
	return nil
}

func MakeSatoriEvent(eventName string, event any) (*runtime.Event, error) {
	eventJson, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}

	satoriEvent := &runtime.Event{
		Name:      eventName,
		Value:     string(eventJson),
		Timestamp: time.Now().Unix(),
	}

	return satoriEvent, nil
}

func PublishMultipleSatoriEvents(ctx context.Context, logger runtime.Logger, eventName string, events []any) {
	eventBody := ""

	satoriEvents := []*runtime.Event{}

	if len(events) != 0 {
		for _, event := range events {

			eventJson, err := json.Marshal(event)
			if err != nil {
				return
			}

			eventBody = string(eventJson)

			satoriEvent := &runtime.Event{
				Name:      eventName,
				Value:     eventBody,
				Timestamp: time.Now().Unix(),
			}

			satoriEvents = append(satoriEvents, satoriEvent)

		}
	}

	userId := GetUserId(ctx)

	if len(userId) != 0 && len(satoriEvents) != 0 {
		logger.WithField("event", satoriEvents).Info("Publishing Satori Event")
		_ = satori.EventsPublish(ctx, userId, satoriEvents)
	}

}

func PublishSatoriEvent(ctx context.Context, logger runtime.Logger, eventName string, event any) {
	eventBody := ""

	if event != nil {
		eventJson, err := json.Marshal(event)
		if err != nil {
			return
		}

		eventBody = string(eventJson)
	}

	satoriEvent := &runtime.Event{
		Name:      eventName,
		Value:     eventBody,
		Timestamp: time.Now().Unix(),
	}

	userId := GetUserId(ctx)

	if len(userId) != 0 {
		logger.WithField("event", *satoriEvent).Info("Publishing Satori Event")
		_ = satori.EventsPublish(ctx, userId, []*runtime.Event{satoriEvent})
	}

}

func PublishSessionEndEvent(ctx context.Context, logger runtime.Logger) {

	PublishSatoriEvent(ctx, logger, "sessionEnd", nil)
}

func GetSatori() runtime.Satori {
	return satori
}

func DeprecatedRPC(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	return "", wl_errors.RPCDeprecated
}
