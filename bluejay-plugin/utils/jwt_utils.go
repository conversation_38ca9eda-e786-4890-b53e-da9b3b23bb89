package utils

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

type satoriTokenClaims struct {
	IdentityId string `json:"iid,omitempty"`
	ExpiresAt  int64  `json:"exp,omitempty"`
	IssuedAt   int64  `json:"iat,omitempty"`
	ApiKeyName string `json:"api,omitempty"`
}

func (stc *satoriTokenClaims) Valid() error {
	if stc.ExpiresAt <= time.Now().UTC().Unix() {
		vErr := new(jwt.ValidationError)
		vErr.Inner = errors.New("token is expired")
		vErr.Errors |= jwt.ValidationErrorExpired
		return vErr
	}
	return nil
}

type satoriTokenGenerator struct {
	apiKeyName string
	signingKey string
}

var SatoriTokenGenerator *satoriTokenGenerator

func InitSatoriTokenGenerator(apiKeyName string, signingKey string) {
	SatoriTokenGenerator = &satoriTokenGenerator{
		apiKeyName: apiKeyName,
		signingKey: signing<PERSON>ey,
	}
}

func (tokenGenerator *satoriTokenGenerator) MakeJwtTokenForPlayer(ctx context.Context, userId string) (string, error) {
	timestamp := time.Now().UTC()
	claims := satoriTokenClaims{
		IdentityId: userId,
		ExpiresAt:  timestamp.Add(1 * time.Hour).Unix(),
		IssuedAt:   timestamp.Unix(),
		ApiKeyName: tokenGenerator.apiKeyName,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, &claims)
	signedToken, err := token.SignedString([]byte(tokenGenerator.signingKey))
	if err != nil {
		return "", fmt.Errorf("Failed to generate Satori jwt token: %w", err)
	}

	return signedToken, nil
}
