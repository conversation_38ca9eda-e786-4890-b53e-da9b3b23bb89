run:

linters-settings:
  govet:
    enable-all: true
    disable:
      - shadow
      - fieldalignment

linters:
  enable-all: true
  disable:
    - cyclop
    - depguard
    - dupl
    - exhaustive
    - errorlint
    - forbidigo
    - funlen
    - gci
    - gochecknoglobals
    - gochecknoinits
    - gocognit
    - gocritic
    - gocyclo
    - godot
    - godox
    - gofumpt
    - gomnd
    - gosec
    - govet
    - inamedparam
    - ireturn # No, I _LIKE_ returning interfaces
    - lll
    - maintidx # Do this in code review
    - makezero
    - nakedret
    - nestif
    - nlreturn
    - nonamedreturns # visit this back later
    - paralleltest
    - tagliatelle
    - testpackage
    - thelper    # Tests are fine
    - varnamelen # Short names are ok
    - wrapcheck
    - wsl

issues:
  exclude-rules:
    # not needed
    - path: /*.go
      text: "ST1003: should not use underscores in package names"
      linters:
        - stylecheck
    - path: /*.go
      text: "don't use an underscore in package name"
      linters:
        - revive
    - path: /*.go
      linters:
        - contextcheck
        - exhaustruct
    - path: /main.go
      linters:
        - errcheck
    - path: /*_test.go
      linters:
        - errcheck
        - errchkjson
        - forcetypeassert
    - path: /*_example_test.go
      linters:
        - forbidigo

  # Maximum issues count per one linter. Set to 0 to disable. Default is 50.
  max-issues-per-linter: 0

  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0

