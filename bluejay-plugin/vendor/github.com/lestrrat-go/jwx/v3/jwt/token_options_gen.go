// Code generated by "stringer -type=TokenOption -output=token_options_gen.go"; DO NOT EDIT.

package jwt

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[FlattenAudience-1]
	_ = x[MaxPerTokenOption-2]
}

const _TokenOption_name = "FlattenAudienceMaxPerTokenOption"

var _TokenOption_index = [...]uint8{0, 15, 32}

func (i TokenOption) String() string {
	i -= 1
	if i >= TokenOption(len(_TokenOption_index)-1) {
		return "TokenOption(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _TokenOption_name[_TokenOption_index[i]:_TokenOption_index[i+1]]
}
