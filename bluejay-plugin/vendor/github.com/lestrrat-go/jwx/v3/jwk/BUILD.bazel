load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jwk",
    srcs = [
        "cache.go",
        "convert.go",
        "ecdsa.go",
        "ecdsa_gen.go",
        "errors.go",
        "fetch.go",
        "interface.go",
        "interface_gen.go",
        "io.go",
        "jwk.go",
        "key_ops.go",
        "okp.go",
        "okp_gen.go",
        "options.go",
        "options_gen.go",
        "parser.go",
        "rsa.go",
        "rsa_gen.go",
        "set.go",
        "symmetric.go",
        "symmetric_gen.go",
        "usage.go",
        "whitelist.go",
        "x509.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/jwk",
    visibility = ["//visibility:public"],
    deps = [
        "//cert",
        "//internal/base64",
        "//internal/ecutil",
        "//internal/json",
        "//internal/pool",
        "//jwa",
        "//jwk/ecdsa",
        "@com_github_lestrrat_go_blackmagic//:blackmagic",
        "@com_github_lestrrat_go_httprc_v3//:httprc",
        "@com_github_lestrrat_go_option//:option",
    ],
)

go_test(
    name = "jwk_test",
    srcs = [
        "headers_test.go",
        "jwk_internal_test.go",
        "jwk_test.go",
        "options_gen_test.go",
        "refresh_test.go",
        "set_test.go",
        "x5c_test.go",
    ],
    data = glob(["testdata/**"]),
    embed = [":jwk"],
    deps = [
        "//cert",
        "//internal/base64",
        "//internal/jose",
        "//internal/json",
        "//internal/jwxtest",
        "//jwa",
        "//jwk/ecdsa",
        "//jws",
        "@com_github_lestrrat_go_httprc_v3//tracesink",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)

alias(
    name = "go_default_library",
    actual = ":jwk",
    visibility = ["//visibility:public"],
)
