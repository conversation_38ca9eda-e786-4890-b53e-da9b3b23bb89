load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "concatkdf",
    srcs = ["concatkdf.go"],
    importpath = "github.com/lestrrat-go/jwx/v3/jwe/internal/concatkdf",
    visibility = ["//:__subpackages__"],
)

go_test(
    name = "concatkdf_test",
    srcs = ["concatkdf_test.go"],
    embed = [":concatkdf"],
    deps = [
        "//jwa",
        "@com_github_stretchr_testify//require",
    ],
)

alias(
    name = "go_default_library",
    actual = ":concatkdf",
    visibility = ["//jwe:__subpackages__"],
)
