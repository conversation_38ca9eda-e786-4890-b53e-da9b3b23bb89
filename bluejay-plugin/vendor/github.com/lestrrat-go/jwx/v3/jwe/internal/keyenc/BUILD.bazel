load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "keyenc",
    srcs = [
        "interface.go",
        "keyenc.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/jwe/internal/keyenc",
    visibility = ["//:__subpackages__"],
    deps = [
        "//internal/ecutil",
        "//jwa",
        "//jwe/internal/cipher",
        "//jwe/internal/concatkdf",
        "//jwe/internal/keygen",
        "@org_golang_x_crypto//pbkdf2",
    ],
)

go_test(
    name = "keyenc_test",
    srcs = ["keyenc_test.go"],
    deps = [
        ":keyenc",
        "//jwk",
        "@com_github_stretchr_testify//require",
    ],
)

alias(
    name = "go_default_library",
    actual = ":keyenc",
    visibility = ["//jwe:__subpackages__"],
)
