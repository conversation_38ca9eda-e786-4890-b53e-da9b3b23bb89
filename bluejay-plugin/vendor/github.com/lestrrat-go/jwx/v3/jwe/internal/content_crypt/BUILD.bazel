load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "content_crypt",
    srcs = [
        "content_crypt.go",
        "interface.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/jwe/internal/content_crypt",
    visibility = ["//:__subpackages__"],
    deps = [
        "//jwa",
        "//jwe/internal/cipher",
    ],
)

alias(
    name = "go_default_library",
    actual = ":content_crypt",
    visibility = ["//jwe:__subpackages__"],
)
