load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jwe",
    srcs = [
        "compress.go",
        "decrypt.go",
        "errors.go",
        "headers.go",
        "headers_gen.go",
        "interface.go",
        "io.go",
        "jwe.go",
        "key_provider.go",
        "message.go",
        "options.go",
        "options_gen.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/jwe",
    visibility = ["//visibility:public"],
    deps = [
        "//cert",
        "//internal/base64",
        "//internal/json",
        "//internal/keyconv",
        "//internal/pool",
        "//jwa",
        "//jwe/internal/aescbc",
        "//jwe/internal/cipher",
        "//jwe/internal/content_crypt",
        "//jwe/internal/keyenc",
        "//jwe/internal/keygen",
        "//jwk",
        "@com_github_lestrrat_go_blackmagic//:blackmagic",
        "@com_github_lestrrat_go_option//:option",
        "@org_golang_x_crypto//pbkdf2",
    ],
)

go_test(
    name = "jwe_test",
    srcs = [
        "gh402_test.go",
        "headers_test.go",
        "jwe_test.go",
        "message_test.go",
        "options_gen_test.go",
        "speed_test.go",
    ],
    embed = [":jwe"],
    deps = [
        "//cert",
        "//internal/json",
        "//internal/jwxtest",
        "//jwa",
        "//jwk",
        "@com_github_stretchr_testify//require",
    ],
)

alias(
    name = "go_default_library",
    actual = ":jwe",
    visibility = ["//visibility:public"],
)
