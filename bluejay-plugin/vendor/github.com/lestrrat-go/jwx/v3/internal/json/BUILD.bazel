load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "json",
    srcs = [
        "json.go",
        "registry.go",
        "stdlib.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/internal/json",
    visibility = ["//:__subpackages__"],
    deps = ["//internal/base64"],
)

alias(
    name = "go_default_library",
    actual = ":json",
    visibility = ["//:__subpackages__"],
)
