load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jws",
    srcs = [
        "ecdsa.go",
        "eddsa.go",
        "errors.go",
        "headers.go",
        "headers_gen.go",
        "hmac.go",
        "interface.go",
        "io.go",
        "jws.go",
        "key_provider.go",
        "message.go",
        "options.go",
        "options_gen.go",
        "rsa.go",
        "signer.go",
        "verifier.go",
    ],
    importpath = "github.com/lestrrat-go/jwx/v3/jws",
    visibility = ["//visibility:public"],
    deps = [
        "//cert",
        "//internal/base64",
        "//internal/ecutil",
        "//internal/json",
        "//internal/keyconv",
        "//internal/pool",
        "//jwa",
        "//jwk",
        "@com_github_lestrrat_go_blackmagic//:blackmagic",
        "@com_github_lestrrat_go_option//:option",
    ],
)

go_test(
    name = "jws_test",
    srcs = [
        "headers_test.go",
        "jws_test.go",
        "message_test.go",
        "options_gen_test.go",
        "signer_test.go",
    ],
    embed = [":jws"],
    deps = [
        "//cert",
        "//internal/base64",
        "//internal/ecutil",
        "//internal/json",
        "//internal/jwxtest",
        "//jwa",
        "//jwk",
        "//jwt",
        "@com_github_lestrrat_go_httprc_v3//:httprc",
        "@com_github_stretchr_testify//require",
    ],
)

alias(
    name = "go_default_library",
    actual = ":jws",
    visibility = ["//visibility:public"],
)
