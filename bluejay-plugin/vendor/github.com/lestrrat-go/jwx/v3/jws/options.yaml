package_name: jws
output: jws/options_gen.go
interfaces:
  - name: CompactOption
    comment: |
      CompactOption describes options that can be passed to `jws.Compact`
  - name: VerifyOption
    comment: |
      VerifyOption describes options that can be passed to `jws.Verify`
    methods:
      - verifyOption
      - parseOption
  - name: SignOption
    comment: |
      SignOption describes options that can be passed to `jws.Sign`
  - name: SignVerifyOption
    methods:
      - signOption
      - verifyOption
      - parseOption
    comment: |
      SignVerifyOption describes options that can be passed to either `jws.Verify` or `jws.Sign`
  - name: SignVerifyCompactOption
    methods:
      - signOption
      - verifyOption
      - compactOption
      - parseOption
    comment: |
      SignVerifyCompactOption describes options that can be passed to either `jws.Verify`,
      `jws.Sign`, or `jws.Compact`
  - name: WithJSONSuboption
    concrete_type: withJSONSuboption
    comment: |
      JSONSuboption describes suboptions that can be passed to the `jws.WithJSON()` option.
  - name: WithKeySuboption
    comment: |
      WithKeySuboption describes option types that can be passed to the `jws.WithKey()`
      option.
  - name: WithKeySetSuboption
    comment: |
      WithKeySetSuboption is a suboption passed to the `jws.WithKeySet()` option
  - name: ParseOption
    methods:
      - readFileOption
    comment: |
      ReadFileOption is a type of `Option` that can be passed to `jwe.Parse`
  - name: ReadFileOption
    comment: |
      ReadFileOption is a type of `Option` that can be passed to `jws.ReadFile`
  - name: SignVerifyParseOption
    methods:
      - signOption
      - verifyOption
      - parseOption
      - readFileOption
options:
  - ident: Key
    skip_option: true
  - ident: Serialization
    skip_option: true
  - ident: Serialization
    option_name: WithCompact
    interface: SignVerifyParseOption
    constant_value: fmtCompact
    comment: |
      WithCompact specifies that the result of `jws.Sign()` is serialized in
      compact format.
      
      By default `jws.Sign()` will opt to use compact format, so you usually
      do not need to specify this option other than to be explicit about it
  - ident: Detached
    interface: CompactOption
    argument_type: bool
    comment: |
      WithDetached specifies that the `jws.Message` should be serialized in
      JWS compact serialization with detached payload. The resulting octet
      sequence will not contain the payload section.
  - ident: DetachedPayload
    interface: SignVerifyOption
    argument_type: '[]byte'
    comment: |
       WithDetachedPayload can be used to both sign or verify a JWS message with a
       detached payload.
       
       When this option is used for `jws.Sign()`, the first parameter (normally the payload)
       must be set to `nil`.
       
       If you have to verify using this option, you should know exactly how and why this works.
  - ident: Base64Encoder
    interface: SignVerifyCompactOption
    argument_type: Base64Encoder
    comment: |
      WithBase64Encoder specifies the base64 encoder to be used while signing or
      verifying the JWS message. By default, the raw URL base64 encoding (no padding)
      is used.
  - ident: Message
    interface: VerifyOption
    argument_type: '*Message'
    comment: |
      WithMessage can be passed to Verify() to obtain the jws.Message upon
      a successful verification.
  - ident: KeyUsed
    interface: VerifyOption
    argument_type: 'interface{}'
    comment: |
      WithKeyUsed allows you to specify the `jws.Verify()` function to
      return the key used for verification. This may be useful when
      you specify multiple key sources or if you pass a `jwk.Set`
      and you want to know which key was successful at verifying the
      signature.
      
      `v` must be a pointer to an empty `interface{}`. Do not use
      `jwk.Key` here unless you are 100% sure that all keys that you
      have provided are instances of `jwk.Key` (remember that the
      jwx API allows users to specify a raw key such as *rsa.PublicKey)
  - ident: ValidateKey
    interface: SignVerifyOption
    argument_type: bool
    comment: |
      WithValidateKey specifies whether the key used for signing or verification
      should be validated before using. Note that this means calling
      `key.Validate()` on the key, which in turn means that your key
      must be a `jwk.Key` instance, or a key that can be converted to
      a `jwk.Key` by calling `jwk.Import()`. This means that your
      custom hardware-backed keys will probably not work.

      You can directly call `key.Validate()` yourself if you need to
      mix keys that cannot be converted to `jwk.Key`.

      Please also note that use of this option will also result in
      one extra conversion of raw keys to a `jwk.Key` instance. If you
      care about shaving off as much as possible, consider using a
      pre-validated key instead of using this option to validate
      the key on-demand each time.

      By default, the key is not validated.
  - ident: InferAlgorithmFromKey
    interface: WithKeySetSuboption
    argument_type: bool
    comment: |
      WithInferAlgorithmFromKey specifies whether the JWS signing algorithm name
      should be inferred by looking at the provided key, in case the JWS
      message or the key does not have a proper `alg` header.

      When this option is set to true, a list of algorithm(s) that is compatible
      with the key type will be enumerated, and _ALL_ of them will be tried
      against the key/message pair. If any of them succeeds, the verification
      will be considered successful.

      Compared to providing explicit `alg` from the key this is slower, and 
      verification may fail to verify if somehow our heuristics are wrong
      or outdated.
      
      Also, automatic detection of signature verification methods are always
      more vulnerable for potential attack vectors.
      
      It is highly recommended that you fix your key to contain a proper `alg`
      header field instead of resorting to using this option, but sometimes
      it just needs to happen.
  - ident: UseDefault
    interface: WithKeySetSuboption
    argument_type: bool
    comment: |
      WithUseDefault specifies that if and only if a jwk.Key contains
      exactly one jwk.Key, that key should be used.
  - ident: RequireKid
    interface: WithKeySetSuboption
    argument_type: bool
    comment: |
      WithRequiredKid specifies whether the keys in the jwk.Set should
      only be matched if the target JWS message's Key ID and the Key ID
      in the given key matches.
  - ident: MultipleKeysPerKeyID
    interface: WithKeySetSuboption
    argument_type: bool
    comment: |
      WithMultipleKeysPerKeyID specifies if we should expect multiple keys
      to match against a key ID. By default it is assumed that key IDs are
      unique, i.e. for a given key ID, the key set only contains a single
      key that has the matching ID. When this option is set to true,
      multiple keys that match the same key ID in the set can be tried.
  - ident: Pretty
    interface: WithJSONSuboption
    argument_type: bool
    comment: |
      WithPretty specifies whether the JSON output should be formatted and
      indented
  - ident: KeyProvider
    interface: VerifyOption
    argument_type: KeyProvider
  - ident: Context
    interface: VerifyOption
    argument_type: context.Context
  - ident: ProtectedHeaders
    interface: WithKeySuboption
    argument_type: Headers
    comment: |
      WithProtected is used with `jws.WithKey()` option when used with `jws.Sign()`
      to specify a protected header to be attached to the JWS signature.
      
      It has no effect if used when `jws.WithKey()` is passed to `jws.Verify()`
  - ident: PublicHeaders
    interface: WithKeySuboption
    argument_type: Headers
    comment: |
      WithPublic is used with `jws.WithKey()` option when used with `jws.Sign()`
      to specify a public header to be attached to the JWS signature.
      
      It has no effect if used when `jws.WithKey()` is passed to `jws.Verify()`
      
      `jws.Sign()` will result in an error if `jws.WithPublic()` is used
      and the serialization format is compact serialization.
  - ident: FS
    interface: ReadFileOption
    argument_type: fs.FS
    comment: |
      WithFS specifies the source `fs.FS` object to read the file from.
