// Code generated by tools/cmd/genoptions/main.go. DO NOT EDIT.

package jwa

import "github.com/lestrrat-go/option"

type Option = option.Interface

// NewKeyEncryptionAlgorithmOption represents an option that can be passed to the NewKeyEncryptionAlgorithm
type NewKeyEncryptionAlgorithmOption interface {
	Option
	newKeyEncryptionAlgorithmOption()
}

type newKeyEncryptionAlgorithmOption struct {
	Option
}

func (*newKeyEncryptionAlgorithmOption) newKeyEncryptionAlgorithmOption() {}

// NewSignatureAlgorithmOption represents an option that can be passed to the NewSignatureAlgorithm
type NewSignatureAlgorithmOption interface {
	Option
	newSignatureAlgorithmOption()
}

type newSignatureAlgorithmOption struct {
	Option
}

func (*newSignatureAlgorithmOption) newSignatureAlgorithmOption() {}

// NewSignatureKeyEncryptionAlgorithmOption represents an option that can be passed to both
// NewSignatureAlgorithm and NewKeyEncryptionAlgorithm
type NewSignatureKeyEncryptionAlgorithmOption interface {
	Option
	newSignatureAlgorithmOption()
	newKeyEncryptionAlgorithmOption()
}

type newSignatureKeyEncryptionAlgorithmOption struct {
	Option
}

func (*newSignatureKeyEncryptionAlgorithmOption) newSignatureAlgorithmOption() {}

func (*newSignatureKeyEncryptionAlgorithmOption) newKeyEncryptionAlgorithmOption() {}

type identIsSymmetric struct{}

func (identIsSymmetric) String() string {
	return "WithIsSymmetric"
}

// IsSymmetric specifies that the algorithm is symmetric
func WithIsSymmetric(v bool) NewSignatureKeyEncryptionAlgorithmOption {
	return &newSignatureKeyEncryptionAlgorithmOption{option.New(identIsSymmetric{}, v)}
}
