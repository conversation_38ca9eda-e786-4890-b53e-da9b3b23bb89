// Code generated by tools/cmd/genjwa/main.go. DO NOT EDIT.

package jwa

import (
	"encoding/json"
	"fmt"
	"sort"
	"sync"
)

var muAllKeyEncryptionAlgorithm sync.RWMutex
var allKeyEncryptionAlgorithm = map[string]KeyEncryptionAlgorithm{}
var muListKeyEncryptionAlgorithm sync.RWMutex
var listKeyEncryptionAlgorithm []KeyEncryptionAlgorithm
var builtinKeyEncryptionAlgorithm = map[string]struct{}{}

func init() {
	// builtin values for KeyEncryptionAlgorithm
	algorithms := make([]KeyEncryptionAlgorithm, 0, 19)
	for _, alg := range []string{"A128GCMKW", "A128KW", "A192GCMKW", "A192KW", "A256GCMKW", "A256KW", "dir", "PBES2-HS256+A128KW", "PBES2-HS384+A192KW", "PBES2-HS512+A256KW"} {
		algorithms = append(algorithms, NewKeyEncryptionAlgorithm(alg, WithIsSymmetric(true)))
	}

	for _, alg := range []string{"ECDH-ES", "ECDH-ES+A128KW", "ECDH-ES+A192KW", "ECDH-ES+A256KW", "RSA1_5", "RSA-OAEP", "RSA-OAEP-256", "RSA-OAEP-384", "RSA-OAEP-512"} {
		algorithms = append(algorithms, NewKeyEncryptionAlgorithm(alg))
	}

	RegisterKeyEncryptionAlgorithm(algorithms...)
}

// A128GCMKW returns an object representing AES-GCM key wrap (128) key encryption algorithm.
func A128GCMKW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A128GCMKW")
}

// A128KW returns an object representing AES key wrap (128) key encryption algorithm.
func A128KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A128KW")
}

// A192GCMKW returns an object representing AES-GCM key wrap (192) key encryption algorithm.
func A192GCMKW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A192GCMKW")
}

// A192KW returns an object representing AES key wrap (192) key encryption algorithm.
func A192KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A192KW")
}

// A256GCMKW returns an object representing AES-GCM key wrap (256) key encryption algorithm.
func A256GCMKW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A256GCMKW")
}

// A256KW returns an object representing AES key wrap (256) key encryption algorithm.
func A256KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("A256KW")
}

// DIRECT returns an object representing Direct key encryption algorithm.
func DIRECT() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("dir")
}

// ECDH_ES returns an object representing ECDH-ES key encryption algorithm.
func ECDH_ES() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("ECDH-ES")
}

// ECDH_ES_A128KW returns an object representing ECDH-ES + AES key wrap (128) key encryption algorithm.
func ECDH_ES_A128KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("ECDH-ES+A128KW")
}

// ECDH_ES_A192KW returns an object representing ECDH-ES + AES key wrap (192) key encryption algorithm.
func ECDH_ES_A192KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("ECDH-ES+A192KW")
}

// ECDH_ES_A256KW returns an object representing ECDH-ES + AES key wrap (256) key encryption algorithm.
func ECDH_ES_A256KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("ECDH-ES+A256KW")
}

// PBES2_HS256_A128KW returns an object representing PBES2 + HMAC-SHA256 + AES key wrap (128) key encryption algorithm.
func PBES2_HS256_A128KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("PBES2-HS256+A128KW")
}

// PBES2_HS384_A192KW returns an object representing PBES2 + HMAC-SHA384 + AES key wrap (192) key encryption algorithm.
func PBES2_HS384_A192KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("PBES2-HS384+A192KW")
}

// PBES2_HS512_A256KW returns an object representing PBES2 + HMAC-SHA512 + AES key wrap (256) key encryption algorithm.
func PBES2_HS512_A256KW() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("PBES2-HS512+A256KW")
}

// RSA1_5 returns an object representing RSA-PKCS1v1.5 key encryption algorithm.
func RSA1_5() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("RSA1_5")
}

// RSA_OAEP returns an object representing RSA-OAEP-SHA1 key encryption algorithm.
func RSA_OAEP() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("RSA-OAEP")
}

// RSA_OAEP_256 returns an object representing RSA-OAEP-SHA256 key encryption algorithm.
func RSA_OAEP_256() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("RSA-OAEP-256")
}

// RSA_OAEP_384 returns an object representing RSA-OAEP-SHA384 key encryption algorithm.
func RSA_OAEP_384() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("RSA-OAEP-384")
}

// RSA_OAEP_512 returns an object representing RSA-OAEP-SHA512 key encryption algorithm.
func RSA_OAEP_512() KeyEncryptionAlgorithm {
	return lookupBuiltinKeyEncryptionAlgorithm("RSA-OAEP-512")
}

func lookupBuiltinKeyEncryptionAlgorithm(name string) KeyEncryptionAlgorithm {
	muAllKeyEncryptionAlgorithm.RLock()
	v, ok := allKeyEncryptionAlgorithm[name]
	muAllKeyEncryptionAlgorithm.RUnlock()
	if !ok {
		panic(fmt.Sprintf(`jwa: KeyEncryptionAlgorithm %q not registered`, name))
	}
	return v
}

// KeyEncryptionAlgorithm represents the various encryption algorithms as described in https://tools.ietf.org/html/rfc7518#section-4.1
type KeyEncryptionAlgorithm struct {
	name        string
	isSymmetric bool
}

func (s KeyEncryptionAlgorithm) String() string {
	return s.name
}

// IsSymmetric returns true if the KeyEncryptionAlgorithm object is symmetric. Symmetric algorithms use the same key for both encryption and decryption.
func (s KeyEncryptionAlgorithm) IsSymmetric() bool {
	return s.isSymmetric
}

// EmptyKeyEncryptionAlgorithm returns an empty KeyEncryptionAlgorithm object, used as a zero value.
func EmptyKeyEncryptionAlgorithm() KeyEncryptionAlgorithm {
	return KeyEncryptionAlgorithm{}
}

// NewKeyEncryptionAlgorithm creates a new KeyEncryptionAlgorithm object with the given name.
func NewKeyEncryptionAlgorithm(name string, options ...NewKeyEncryptionAlgorithmOption) KeyEncryptionAlgorithm {
	var isSymmetric bool
	//nolint:forcetypeassert
	for _, option := range options {
		switch option.Ident() {
		case identIsSymmetric{}:
			isSymmetric = option.Value().(bool)
		}
	}
	return KeyEncryptionAlgorithm{name: name, isSymmetric: isSymmetric}
}

// LookupKeyEncryptionAlgorithm returns the KeyEncryptionAlgorithm object for the given name.
func LookupKeyEncryptionAlgorithm(name string) (KeyEncryptionAlgorithm, bool) {
	muAllKeyEncryptionAlgorithm.RLock()
	v, ok := allKeyEncryptionAlgorithm[name]
	muAllKeyEncryptionAlgorithm.RUnlock()
	return v, ok
}

// RegisterKeyEncryptionAlgorithm registers a new KeyEncryptionAlgorithm. The signature value must be immutable
// and safe to be used by multiple goroutines, as it is going to be shared with all other users of this library.
func RegisterKeyEncryptionAlgorithm(algorithms ...KeyEncryptionAlgorithm) {
	muAllKeyEncryptionAlgorithm.Lock()
	for _, alg := range algorithms {
		allKeyEncryptionAlgorithm[alg.String()] = alg
	}
	muAllKeyEncryptionAlgorithm.Unlock()
	rebuildKeyEncryptionAlgorithm()
}

// UnregisterKeyEncryptionAlgorithm unregisters a KeyEncryptionAlgorithm from its known database.
// Non-existent entries, as well as built-in algorithms will silently be ignored.
func UnregisterKeyEncryptionAlgorithm(algorithms ...KeyEncryptionAlgorithm) {
	muAllKeyEncryptionAlgorithm.Lock()
	for _, alg := range algorithms {
		if _, ok := builtinKeyEncryptionAlgorithm[alg.String()]; ok {
			continue
		}
		delete(allKeyEncryptionAlgorithm, alg.String())
	}
	muAllKeyEncryptionAlgorithm.Unlock()
	rebuildKeyEncryptionAlgorithm()
}

func rebuildKeyEncryptionAlgorithm() {
	list := make([]KeyEncryptionAlgorithm, 0, len(allKeyEncryptionAlgorithm))
	muAllKeyEncryptionAlgorithm.RLock()
	for _, v := range allKeyEncryptionAlgorithm {
		list = append(list, v)
	}
	muAllKeyEncryptionAlgorithm.RUnlock()
	sort.Slice(list, func(i, j int) bool {
		return list[i].String() < list[j].String()
	})
	muListKeyEncryptionAlgorithm.Lock()
	listKeyEncryptionAlgorithm = list
	muListKeyEncryptionAlgorithm.Unlock()
}

// KeyEncryptionAlgorithms returns a list of all available values for KeyEncryptionAlgorithm.
func KeyEncryptionAlgorithms() []KeyEncryptionAlgorithm {
	muListKeyEncryptionAlgorithm.RLock()
	defer muListKeyEncryptionAlgorithm.RUnlock()
	return listKeyEncryptionAlgorithm
}

// MarshalJSON serializes the KeyEncryptionAlgorithm object to a JSON string.
func (s KeyEncryptionAlgorithm) MarshalJSON() ([]byte, error) {
	return json.Marshal(s.String())
}

// UnmarshalJSON deserializes the JSON string to a KeyEncryptionAlgorithm object.
func (s *KeyEncryptionAlgorithm) UnmarshalJSON(data []byte) error {
	var name string
	if err := json.Unmarshal(data, &name); err != nil {
		return fmt.Errorf(`failed to unmarshal KeyEncryptionAlgorithm: %w`, err)
	}
	v, ok := LookupKeyEncryptionAlgorithm(name)
	if !ok {
		return fmt.Errorf(`unknown KeyEncryptionAlgorithm: %s`, name)
	}
	*s = v
	return nil
}
