# Security Policy

## Supported Versions

As of February 2022 (and until this document is updated), the latest version `v4` is supported.

## Reporting a Vulnerability

If you think you found a vulnerability, and even if you are not sure, please report <NAME_EMAIL> or one of the other [golang-jwt maintainers](https://github.com/orgs/golang-jwt/people). Please try be explicit, describe steps to reproduce the security issue with code example(s).

You will receive a response within a timely manner. If the issue is confirmed, we will do our best to release a patch as soon as possible given the complexity of the problem.

## Public Discussions

Please avoid publicly discussing a potential security vulnerability.

Let's take this offline and find a solution first, this limits the potential impact as much as possible.

We appreciate your help!
