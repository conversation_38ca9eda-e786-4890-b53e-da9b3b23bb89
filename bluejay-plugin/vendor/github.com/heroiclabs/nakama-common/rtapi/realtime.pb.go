// Copyright 2019 The Nakama Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//*
// The realtime protocol for Nakama server.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v5.29.3
// source: realtime.proto

package rtapi

import (
	api "github.com/heroiclabs/nakama-common/api"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The type of chat channel.
type ChannelJoin_Type int32

const (
	// Default case. Assumed as ROOM type.
	ChannelJoin_TYPE_UNSPECIFIED ChannelJoin_Type = 0
	// A room which anyone can join to chat.
	ChannelJoin_ROOM ChannelJoin_Type = 1
	// A private channel for 1-on-1 chat.
	ChannelJoin_DIRECT_MESSAGE ChannelJoin_Type = 2
	// A channel for group chat.
	ChannelJoin_GROUP ChannelJoin_Type = 3
)

// Enum value maps for ChannelJoin_Type.
var (
	ChannelJoin_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "ROOM",
		2: "DIRECT_MESSAGE",
		3: "GROUP",
	}
	ChannelJoin_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"ROOM":             1,
		"DIRECT_MESSAGE":   2,
		"GROUP":            3,
	}
)

func (x ChannelJoin_Type) Enum() *ChannelJoin_Type {
	p := new(ChannelJoin_Type)
	*p = x
	return p
}

func (x ChannelJoin_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelJoin_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_realtime_proto_enumTypes[0].Descriptor()
}

func (ChannelJoin_Type) Type() protoreflect.EnumType {
	return &file_realtime_proto_enumTypes[0]
}

func (x ChannelJoin_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelJoin_Type.Descriptor instead.
func (ChannelJoin_Type) EnumDescriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{2, 0}
}

// The selection of possible error codes.
type Error_Code int32

const (
	// An unexpected result from the server.
	Error_RUNTIME_EXCEPTION Error_Code = 0
	// The server received a message which is not recognised.
	Error_UNRECOGNIZED_PAYLOAD Error_Code = 1
	// A message was expected but contains no content.
	Error_MISSING_PAYLOAD Error_Code = 2
	// Fields in the message have an invalid format.
	Error_BAD_INPUT Error_Code = 3
	// The match id was not found.
	Error_MATCH_NOT_FOUND Error_Code = 4
	// The match join was rejected.
	Error_MATCH_JOIN_REJECTED Error_Code = 5
	// The runtime function does not exist on the server.
	Error_RUNTIME_FUNCTION_NOT_FOUND Error_Code = 6
	// The runtime function executed with an error.
	Error_RUNTIME_FUNCTION_EXCEPTION Error_Code = 7
)

// Enum value maps for Error_Code.
var (
	Error_Code_name = map[int32]string{
		0: "RUNTIME_EXCEPTION",
		1: "UNRECOGNIZED_PAYLOAD",
		2: "MISSING_PAYLOAD",
		3: "BAD_INPUT",
		4: "MATCH_NOT_FOUND",
		5: "MATCH_JOIN_REJECTED",
		6: "RUNTIME_FUNCTION_NOT_FOUND",
		7: "RUNTIME_FUNCTION_EXCEPTION",
	}
	Error_Code_value = map[string]int32{
		"RUNTIME_EXCEPTION":          0,
		"UNRECOGNIZED_PAYLOAD":       1,
		"MISSING_PAYLOAD":            2,
		"BAD_INPUT":                  3,
		"MATCH_NOT_FOUND":            4,
		"MATCH_JOIN_REJECTED":        5,
		"RUNTIME_FUNCTION_NOT_FOUND": 6,
		"RUNTIME_FUNCTION_EXCEPTION": 7,
	}
)

func (x Error_Code) Enum() *Error_Code {
	p := new(Error_Code)
	*p = x
	return p
}

func (x Error_Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Error_Code) Descriptor() protoreflect.EnumDescriptor {
	return file_realtime_proto_enumTypes[1].Descriptor()
}

func (Error_Code) Type() protoreflect.EnumType {
	return &file_realtime_proto_enumTypes[1]
}

func (x Error_Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Error_Code.Descriptor instead.
func (Error_Code) EnumDescriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{9, 0}
}

// An envelope for a realtime message.
type Envelope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cid string `protobuf:"bytes,1,opt,name=cid,proto3" json:"cid,omitempty"`
	// Types that are assignable to Message:
	//
	//	*Envelope_Channel
	//	*Envelope_ChannelJoin
	//	*Envelope_ChannelLeave
	//	*Envelope_ChannelMessage
	//	*Envelope_ChannelMessageAck
	//	*Envelope_ChannelMessageSend
	//	*Envelope_ChannelMessageUpdate
	//	*Envelope_ChannelMessageRemove
	//	*Envelope_ChannelPresenceEvent
	//	*Envelope_Error
	//	*Envelope_Match
	//	*Envelope_MatchCreate
	//	*Envelope_MatchData
	//	*Envelope_MatchDataSend
	//	*Envelope_MatchJoin
	//	*Envelope_MatchLeave
	//	*Envelope_MatchPresenceEvent
	//	*Envelope_MatchmakerAdd
	//	*Envelope_MatchmakerMatched
	//	*Envelope_MatchmakerRemove
	//	*Envelope_MatchmakerTicket
	//	*Envelope_Notifications
	//	*Envelope_Rpc
	//	*Envelope_Status
	//	*Envelope_StatusFollow
	//	*Envelope_StatusPresenceEvent
	//	*Envelope_StatusUnfollow
	//	*Envelope_StatusUpdate
	//	*Envelope_StreamData
	//	*Envelope_StreamPresenceEvent
	//	*Envelope_Ping
	//	*Envelope_Pong
	//	*Envelope_Party
	//	*Envelope_PartyCreate
	//	*Envelope_PartyJoin
	//	*Envelope_PartyLeave
	//	*Envelope_PartyPromote
	//	*Envelope_PartyLeader
	//	*Envelope_PartyAccept
	//	*Envelope_PartyRemove
	//	*Envelope_PartyClose
	//	*Envelope_PartyJoinRequestList
	//	*Envelope_PartyJoinRequest
	//	*Envelope_PartyMatchmakerAdd
	//	*Envelope_PartyMatchmakerRemove
	//	*Envelope_PartyMatchmakerTicket
	//	*Envelope_PartyData
	//	*Envelope_PartyDataSend
	//	*Envelope_PartyPresenceEvent
	Message isEnvelope_Message `protobuf_oneof:"message"`
}

func (x *Envelope) Reset() {
	*x = Envelope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Envelope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Envelope) ProtoMessage() {}

func (x *Envelope) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Envelope.ProtoReflect.Descriptor instead.
func (*Envelope) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{0}
}

func (x *Envelope) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (m *Envelope) GetMessage() isEnvelope_Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (x *Envelope) GetChannel() *Channel {
	if x, ok := x.GetMessage().(*Envelope_Channel); ok {
		return x.Channel
	}
	return nil
}

func (x *Envelope) GetChannelJoin() *ChannelJoin {
	if x, ok := x.GetMessage().(*Envelope_ChannelJoin); ok {
		return x.ChannelJoin
	}
	return nil
}

func (x *Envelope) GetChannelLeave() *ChannelLeave {
	if x, ok := x.GetMessage().(*Envelope_ChannelLeave); ok {
		return x.ChannelLeave
	}
	return nil
}

func (x *Envelope) GetChannelMessage() *api.ChannelMessage {
	if x, ok := x.GetMessage().(*Envelope_ChannelMessage); ok {
		return x.ChannelMessage
	}
	return nil
}

func (x *Envelope) GetChannelMessageAck() *ChannelMessageAck {
	if x, ok := x.GetMessage().(*Envelope_ChannelMessageAck); ok {
		return x.ChannelMessageAck
	}
	return nil
}

func (x *Envelope) GetChannelMessageSend() *ChannelMessageSend {
	if x, ok := x.GetMessage().(*Envelope_ChannelMessageSend); ok {
		return x.ChannelMessageSend
	}
	return nil
}

func (x *Envelope) GetChannelMessageUpdate() *ChannelMessageUpdate {
	if x, ok := x.GetMessage().(*Envelope_ChannelMessageUpdate); ok {
		return x.ChannelMessageUpdate
	}
	return nil
}

func (x *Envelope) GetChannelMessageRemove() *ChannelMessageRemove {
	if x, ok := x.GetMessage().(*Envelope_ChannelMessageRemove); ok {
		return x.ChannelMessageRemove
	}
	return nil
}

func (x *Envelope) GetChannelPresenceEvent() *ChannelPresenceEvent {
	if x, ok := x.GetMessage().(*Envelope_ChannelPresenceEvent); ok {
		return x.ChannelPresenceEvent
	}
	return nil
}

func (x *Envelope) GetError() *Error {
	if x, ok := x.GetMessage().(*Envelope_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Envelope) GetMatch() *Match {
	if x, ok := x.GetMessage().(*Envelope_Match); ok {
		return x.Match
	}
	return nil
}

func (x *Envelope) GetMatchCreate() *MatchCreate {
	if x, ok := x.GetMessage().(*Envelope_MatchCreate); ok {
		return x.MatchCreate
	}
	return nil
}

func (x *Envelope) GetMatchData() *MatchData {
	if x, ok := x.GetMessage().(*Envelope_MatchData); ok {
		return x.MatchData
	}
	return nil
}

func (x *Envelope) GetMatchDataSend() *MatchDataSend {
	if x, ok := x.GetMessage().(*Envelope_MatchDataSend); ok {
		return x.MatchDataSend
	}
	return nil
}

func (x *Envelope) GetMatchJoin() *MatchJoin {
	if x, ok := x.GetMessage().(*Envelope_MatchJoin); ok {
		return x.MatchJoin
	}
	return nil
}

func (x *Envelope) GetMatchLeave() *MatchLeave {
	if x, ok := x.GetMessage().(*Envelope_MatchLeave); ok {
		return x.MatchLeave
	}
	return nil
}

func (x *Envelope) GetMatchPresenceEvent() *MatchPresenceEvent {
	if x, ok := x.GetMessage().(*Envelope_MatchPresenceEvent); ok {
		return x.MatchPresenceEvent
	}
	return nil
}

func (x *Envelope) GetMatchmakerAdd() *MatchmakerAdd {
	if x, ok := x.GetMessage().(*Envelope_MatchmakerAdd); ok {
		return x.MatchmakerAdd
	}
	return nil
}

func (x *Envelope) GetMatchmakerMatched() *MatchmakerMatched {
	if x, ok := x.GetMessage().(*Envelope_MatchmakerMatched); ok {
		return x.MatchmakerMatched
	}
	return nil
}

func (x *Envelope) GetMatchmakerRemove() *MatchmakerRemove {
	if x, ok := x.GetMessage().(*Envelope_MatchmakerRemove); ok {
		return x.MatchmakerRemove
	}
	return nil
}

func (x *Envelope) GetMatchmakerTicket() *MatchmakerTicket {
	if x, ok := x.GetMessage().(*Envelope_MatchmakerTicket); ok {
		return x.MatchmakerTicket
	}
	return nil
}

func (x *Envelope) GetNotifications() *Notifications {
	if x, ok := x.GetMessage().(*Envelope_Notifications); ok {
		return x.Notifications
	}
	return nil
}

func (x *Envelope) GetRpc() *api.Rpc {
	if x, ok := x.GetMessage().(*Envelope_Rpc); ok {
		return x.Rpc
	}
	return nil
}

func (x *Envelope) GetStatus() *Status {
	if x, ok := x.GetMessage().(*Envelope_Status); ok {
		return x.Status
	}
	return nil
}

func (x *Envelope) GetStatusFollow() *StatusFollow {
	if x, ok := x.GetMessage().(*Envelope_StatusFollow); ok {
		return x.StatusFollow
	}
	return nil
}

func (x *Envelope) GetStatusPresenceEvent() *StatusPresenceEvent {
	if x, ok := x.GetMessage().(*Envelope_StatusPresenceEvent); ok {
		return x.StatusPresenceEvent
	}
	return nil
}

func (x *Envelope) GetStatusUnfollow() *StatusUnfollow {
	if x, ok := x.GetMessage().(*Envelope_StatusUnfollow); ok {
		return x.StatusUnfollow
	}
	return nil
}

func (x *Envelope) GetStatusUpdate() *StatusUpdate {
	if x, ok := x.GetMessage().(*Envelope_StatusUpdate); ok {
		return x.StatusUpdate
	}
	return nil
}

func (x *Envelope) GetStreamData() *StreamData {
	if x, ok := x.GetMessage().(*Envelope_StreamData); ok {
		return x.StreamData
	}
	return nil
}

func (x *Envelope) GetStreamPresenceEvent() *StreamPresenceEvent {
	if x, ok := x.GetMessage().(*Envelope_StreamPresenceEvent); ok {
		return x.StreamPresenceEvent
	}
	return nil
}

func (x *Envelope) GetPing() *Ping {
	if x, ok := x.GetMessage().(*Envelope_Ping); ok {
		return x.Ping
	}
	return nil
}

func (x *Envelope) GetPong() *Pong {
	if x, ok := x.GetMessage().(*Envelope_Pong); ok {
		return x.Pong
	}
	return nil
}

func (x *Envelope) GetParty() *Party {
	if x, ok := x.GetMessage().(*Envelope_Party); ok {
		return x.Party
	}
	return nil
}

func (x *Envelope) GetPartyCreate() *PartyCreate {
	if x, ok := x.GetMessage().(*Envelope_PartyCreate); ok {
		return x.PartyCreate
	}
	return nil
}

func (x *Envelope) GetPartyJoin() *PartyJoin {
	if x, ok := x.GetMessage().(*Envelope_PartyJoin); ok {
		return x.PartyJoin
	}
	return nil
}

func (x *Envelope) GetPartyLeave() *PartyLeave {
	if x, ok := x.GetMessage().(*Envelope_PartyLeave); ok {
		return x.PartyLeave
	}
	return nil
}

func (x *Envelope) GetPartyPromote() *PartyPromote {
	if x, ok := x.GetMessage().(*Envelope_PartyPromote); ok {
		return x.PartyPromote
	}
	return nil
}

func (x *Envelope) GetPartyLeader() *PartyLeader {
	if x, ok := x.GetMessage().(*Envelope_PartyLeader); ok {
		return x.PartyLeader
	}
	return nil
}

func (x *Envelope) GetPartyAccept() *PartyAccept {
	if x, ok := x.GetMessage().(*Envelope_PartyAccept); ok {
		return x.PartyAccept
	}
	return nil
}

func (x *Envelope) GetPartyRemove() *PartyRemove {
	if x, ok := x.GetMessage().(*Envelope_PartyRemove); ok {
		return x.PartyRemove
	}
	return nil
}

func (x *Envelope) GetPartyClose() *PartyClose {
	if x, ok := x.GetMessage().(*Envelope_PartyClose); ok {
		return x.PartyClose
	}
	return nil
}

func (x *Envelope) GetPartyJoinRequestList() *PartyJoinRequestList {
	if x, ok := x.GetMessage().(*Envelope_PartyJoinRequestList); ok {
		return x.PartyJoinRequestList
	}
	return nil
}

func (x *Envelope) GetPartyJoinRequest() *PartyJoinRequest {
	if x, ok := x.GetMessage().(*Envelope_PartyJoinRequest); ok {
		return x.PartyJoinRequest
	}
	return nil
}

func (x *Envelope) GetPartyMatchmakerAdd() *PartyMatchmakerAdd {
	if x, ok := x.GetMessage().(*Envelope_PartyMatchmakerAdd); ok {
		return x.PartyMatchmakerAdd
	}
	return nil
}

func (x *Envelope) GetPartyMatchmakerRemove() *PartyMatchmakerRemove {
	if x, ok := x.GetMessage().(*Envelope_PartyMatchmakerRemove); ok {
		return x.PartyMatchmakerRemove
	}
	return nil
}

func (x *Envelope) GetPartyMatchmakerTicket() *PartyMatchmakerTicket {
	if x, ok := x.GetMessage().(*Envelope_PartyMatchmakerTicket); ok {
		return x.PartyMatchmakerTicket
	}
	return nil
}

func (x *Envelope) GetPartyData() *PartyData {
	if x, ok := x.GetMessage().(*Envelope_PartyData); ok {
		return x.PartyData
	}
	return nil
}

func (x *Envelope) GetPartyDataSend() *PartyDataSend {
	if x, ok := x.GetMessage().(*Envelope_PartyDataSend); ok {
		return x.PartyDataSend
	}
	return nil
}

func (x *Envelope) GetPartyPresenceEvent() *PartyPresenceEvent {
	if x, ok := x.GetMessage().(*Envelope_PartyPresenceEvent); ok {
		return x.PartyPresenceEvent
	}
	return nil
}

type isEnvelope_Message interface {
	isEnvelope_Message()
}

type Envelope_Channel struct {
	// A response from a channel join operation.
	Channel *Channel `protobuf:"bytes,2,opt,name=channel,proto3,oneof"`
}

type Envelope_ChannelJoin struct {
	// Join a realtime chat channel.
	ChannelJoin *ChannelJoin `protobuf:"bytes,3,opt,name=channel_join,json=channelJoin,proto3,oneof"`
}

type Envelope_ChannelLeave struct {
	// Leave a realtime chat channel.
	ChannelLeave *ChannelLeave `protobuf:"bytes,4,opt,name=channel_leave,json=channelLeave,proto3,oneof"`
}

type Envelope_ChannelMessage struct {
	// An incoming message on a realtime chat channel.
	ChannelMessage *api.ChannelMessage `protobuf:"bytes,5,opt,name=channel_message,json=channelMessage,proto3,oneof"`
}

type Envelope_ChannelMessageAck struct {
	// An acknowledgement received in response to sending a message on a chat channel.
	ChannelMessageAck *ChannelMessageAck `protobuf:"bytes,6,opt,name=channel_message_ack,json=channelMessageAck,proto3,oneof"`
}

type Envelope_ChannelMessageSend struct {
	// Send a message to a realtime chat channel.
	ChannelMessageSend *ChannelMessageSend `protobuf:"bytes,7,opt,name=channel_message_send,json=channelMessageSend,proto3,oneof"`
}

type Envelope_ChannelMessageUpdate struct {
	// Update a message previously sent to a realtime chat channel.
	ChannelMessageUpdate *ChannelMessageUpdate `protobuf:"bytes,8,opt,name=channel_message_update,json=channelMessageUpdate,proto3,oneof"`
}

type Envelope_ChannelMessageRemove struct {
	// Remove a message previously sent to a realtime chat channel.
	ChannelMessageRemove *ChannelMessageRemove `protobuf:"bytes,9,opt,name=channel_message_remove,json=channelMessageRemove,proto3,oneof"`
}

type Envelope_ChannelPresenceEvent struct {
	// Presence update for a particular realtime chat channel.
	ChannelPresenceEvent *ChannelPresenceEvent `protobuf:"bytes,10,opt,name=channel_presence_event,json=channelPresenceEvent,proto3,oneof"`
}

type Envelope_Error struct {
	// Describes an error which occurred on the server.
	Error *Error `protobuf:"bytes,11,opt,name=error,proto3,oneof"`
}

type Envelope_Match struct {
	// Incoming information about a realtime match.
	Match *Match `protobuf:"bytes,12,opt,name=match,proto3,oneof"`
}

type Envelope_MatchCreate struct {
	// A client to server request to create a realtime match.
	MatchCreate *MatchCreate `protobuf:"bytes,13,opt,name=match_create,json=matchCreate,proto3,oneof"`
}

type Envelope_MatchData struct {
	// Incoming realtime match data delivered from the server.
	MatchData *MatchData `protobuf:"bytes,14,opt,name=match_data,json=matchData,proto3,oneof"`
}

type Envelope_MatchDataSend struct {
	// A client to server request to send data to a realtime match.
	MatchDataSend *MatchDataSend `protobuf:"bytes,15,opt,name=match_data_send,json=matchDataSend,proto3,oneof"`
}

type Envelope_MatchJoin struct {
	// A client to server request to join a realtime match.
	MatchJoin *MatchJoin `protobuf:"bytes,16,opt,name=match_join,json=matchJoin,proto3,oneof"`
}

type Envelope_MatchLeave struct {
	// A client to server request to leave a realtime match.
	MatchLeave *MatchLeave `protobuf:"bytes,17,opt,name=match_leave,json=matchLeave,proto3,oneof"`
}

type Envelope_MatchPresenceEvent struct {
	// Presence update for a particular realtime match.
	MatchPresenceEvent *MatchPresenceEvent `protobuf:"bytes,18,opt,name=match_presence_event,json=matchPresenceEvent,proto3,oneof"`
}

type Envelope_MatchmakerAdd struct {
	// Submit a new matchmaking process request.
	MatchmakerAdd *MatchmakerAdd `protobuf:"bytes,19,opt,name=matchmaker_add,json=matchmakerAdd,proto3,oneof"`
}

type Envelope_MatchmakerMatched struct {
	// A successful matchmaking result.
	MatchmakerMatched *MatchmakerMatched `protobuf:"bytes,20,opt,name=matchmaker_matched,json=matchmakerMatched,proto3,oneof"`
}

type Envelope_MatchmakerRemove struct {
	// Cancel a matchmaking process using a ticket.
	MatchmakerRemove *MatchmakerRemove `protobuf:"bytes,21,opt,name=matchmaker_remove,json=matchmakerRemove,proto3,oneof"`
}

type Envelope_MatchmakerTicket struct {
	// A response from starting a new matchmaking process.
	MatchmakerTicket *MatchmakerTicket `protobuf:"bytes,22,opt,name=matchmaker_ticket,json=matchmakerTicket,proto3,oneof"`
}

type Envelope_Notifications struct {
	// Notifications send by the server.
	Notifications *Notifications `protobuf:"bytes,23,opt,name=notifications,proto3,oneof"`
}

type Envelope_Rpc struct {
	// RPC call or response.
	Rpc *api.Rpc `protobuf:"bytes,24,opt,name=rpc,proto3,oneof"`
}

type Envelope_Status struct {
	// An incoming status snapshot for some set of users.
	Status *Status `protobuf:"bytes,25,opt,name=status,proto3,oneof"`
}

type Envelope_StatusFollow struct {
	// Start following some set of users to receive their status updates.
	StatusFollow *StatusFollow `protobuf:"bytes,26,opt,name=status_follow,json=statusFollow,proto3,oneof"`
}

type Envelope_StatusPresenceEvent struct {
	// An incoming status update.
	StatusPresenceEvent *StatusPresenceEvent `protobuf:"bytes,27,opt,name=status_presence_event,json=statusPresenceEvent,proto3,oneof"`
}

type Envelope_StatusUnfollow struct {
	// Stop following some set of users to no longer receive their status updates.
	StatusUnfollow *StatusUnfollow `protobuf:"bytes,28,opt,name=status_unfollow,json=statusUnfollow,proto3,oneof"`
}

type Envelope_StatusUpdate struct {
	// Set the user's own status.
	StatusUpdate *StatusUpdate `protobuf:"bytes,29,opt,name=status_update,json=statusUpdate,proto3,oneof"`
}

type Envelope_StreamData struct {
	// A data message delivered over a stream.
	StreamData *StreamData `protobuf:"bytes,30,opt,name=stream_data,json=streamData,proto3,oneof"`
}

type Envelope_StreamPresenceEvent struct {
	// Presence update for a particular stream.
	StreamPresenceEvent *StreamPresenceEvent `protobuf:"bytes,31,opt,name=stream_presence_event,json=streamPresenceEvent,proto3,oneof"`
}

type Envelope_Ping struct {
	// Application-level heartbeat and connection check.
	Ping *Ping `protobuf:"bytes,32,opt,name=ping,proto3,oneof"`
}

type Envelope_Pong struct {
	// Application-level heartbeat and connection check response.
	Pong *Pong `protobuf:"bytes,33,opt,name=pong,proto3,oneof"`
}

type Envelope_Party struct {
	// Incoming information about a party.
	Party *Party `protobuf:"bytes,34,opt,name=party,proto3,oneof"`
}

type Envelope_PartyCreate struct {
	// Create a party.
	PartyCreate *PartyCreate `protobuf:"bytes,35,opt,name=party_create,json=partyCreate,proto3,oneof"`
}

type Envelope_PartyJoin struct {
	// Join a party, or request to join if the party is not open.
	PartyJoin *PartyJoin `protobuf:"bytes,36,opt,name=party_join,json=partyJoin,proto3,oneof"`
}

type Envelope_PartyLeave struct {
	// Leave a party.
	PartyLeave *PartyLeave `protobuf:"bytes,37,opt,name=party_leave,json=partyLeave,proto3,oneof"`
}

type Envelope_PartyPromote struct {
	// Promote a new party leader.
	PartyPromote *PartyPromote `protobuf:"bytes,38,opt,name=party_promote,json=partyPromote,proto3,oneof"`
}

type Envelope_PartyLeader struct {
	// Announcement of a new party leader.
	PartyLeader *PartyLeader `protobuf:"bytes,39,opt,name=party_leader,json=partyLeader,proto3,oneof"`
}

type Envelope_PartyAccept struct {
	// Accept a request to join.
	PartyAccept *PartyAccept `protobuf:"bytes,40,opt,name=party_accept,json=partyAccept,proto3,oneof"`
}

type Envelope_PartyRemove struct {
	// Kick a party member, or decline a request to join.
	PartyRemove *PartyRemove `protobuf:"bytes,41,opt,name=party_remove,json=partyRemove,proto3,oneof"`
}

type Envelope_PartyClose struct {
	// End a party, kicking all party members and closing it.
	PartyClose *PartyClose `protobuf:"bytes,42,opt,name=party_close,json=partyClose,proto3,oneof"`
}

type Envelope_PartyJoinRequestList struct {
	// Request a list of pending join requests for a party.
	PartyJoinRequestList *PartyJoinRequestList `protobuf:"bytes,43,opt,name=party_join_request_list,json=partyJoinRequestList,proto3,oneof"`
}

type Envelope_PartyJoinRequest struct {
	// Incoming notification for one or more new presences attempting to join the party.
	PartyJoinRequest *PartyJoinRequest `protobuf:"bytes,44,opt,name=party_join_request,json=partyJoinRequest,proto3,oneof"`
}

type Envelope_PartyMatchmakerAdd struct {
	// Begin matchmaking as a party.
	PartyMatchmakerAdd *PartyMatchmakerAdd `protobuf:"bytes,45,opt,name=party_matchmaker_add,json=partyMatchmakerAdd,proto3,oneof"`
}

type Envelope_PartyMatchmakerRemove struct {
	// Cancel a party matchmaking process using a ticket.
	PartyMatchmakerRemove *PartyMatchmakerRemove `protobuf:"bytes,46,opt,name=party_matchmaker_remove,json=partyMatchmakerRemove,proto3,oneof"`
}

type Envelope_PartyMatchmakerTicket struct {
	// A response from starting a new party matchmaking process.
	PartyMatchmakerTicket *PartyMatchmakerTicket `protobuf:"bytes,47,opt,name=party_matchmaker_ticket,json=partyMatchmakerTicket,proto3,oneof"`
}

type Envelope_PartyData struct {
	// Incoming party data delivered from the server.
	PartyData *PartyData `protobuf:"bytes,48,opt,name=party_data,json=partyData,proto3,oneof"`
}

type Envelope_PartyDataSend struct {
	// A client to server request to send data to a party.
	PartyDataSend *PartyDataSend `protobuf:"bytes,49,opt,name=party_data_send,json=partyDataSend,proto3,oneof"`
}

type Envelope_PartyPresenceEvent struct {
	// Presence update for a particular party.
	PartyPresenceEvent *PartyPresenceEvent `protobuf:"bytes,50,opt,name=party_presence_event,json=partyPresenceEvent,proto3,oneof"`
}

func (*Envelope_Channel) isEnvelope_Message() {}

func (*Envelope_ChannelJoin) isEnvelope_Message() {}

func (*Envelope_ChannelLeave) isEnvelope_Message() {}

func (*Envelope_ChannelMessage) isEnvelope_Message() {}

func (*Envelope_ChannelMessageAck) isEnvelope_Message() {}

func (*Envelope_ChannelMessageSend) isEnvelope_Message() {}

func (*Envelope_ChannelMessageUpdate) isEnvelope_Message() {}

func (*Envelope_ChannelMessageRemove) isEnvelope_Message() {}

func (*Envelope_ChannelPresenceEvent) isEnvelope_Message() {}

func (*Envelope_Error) isEnvelope_Message() {}

func (*Envelope_Match) isEnvelope_Message() {}

func (*Envelope_MatchCreate) isEnvelope_Message() {}

func (*Envelope_MatchData) isEnvelope_Message() {}

func (*Envelope_MatchDataSend) isEnvelope_Message() {}

func (*Envelope_MatchJoin) isEnvelope_Message() {}

func (*Envelope_MatchLeave) isEnvelope_Message() {}

func (*Envelope_MatchPresenceEvent) isEnvelope_Message() {}

func (*Envelope_MatchmakerAdd) isEnvelope_Message() {}

func (*Envelope_MatchmakerMatched) isEnvelope_Message() {}

func (*Envelope_MatchmakerRemove) isEnvelope_Message() {}

func (*Envelope_MatchmakerTicket) isEnvelope_Message() {}

func (*Envelope_Notifications) isEnvelope_Message() {}

func (*Envelope_Rpc) isEnvelope_Message() {}

func (*Envelope_Status) isEnvelope_Message() {}

func (*Envelope_StatusFollow) isEnvelope_Message() {}

func (*Envelope_StatusPresenceEvent) isEnvelope_Message() {}

func (*Envelope_StatusUnfollow) isEnvelope_Message() {}

func (*Envelope_StatusUpdate) isEnvelope_Message() {}

func (*Envelope_StreamData) isEnvelope_Message() {}

func (*Envelope_StreamPresenceEvent) isEnvelope_Message() {}

func (*Envelope_Ping) isEnvelope_Message() {}

func (*Envelope_Pong) isEnvelope_Message() {}

func (*Envelope_Party) isEnvelope_Message() {}

func (*Envelope_PartyCreate) isEnvelope_Message() {}

func (*Envelope_PartyJoin) isEnvelope_Message() {}

func (*Envelope_PartyLeave) isEnvelope_Message() {}

func (*Envelope_PartyPromote) isEnvelope_Message() {}

func (*Envelope_PartyLeader) isEnvelope_Message() {}

func (*Envelope_PartyAccept) isEnvelope_Message() {}

func (*Envelope_PartyRemove) isEnvelope_Message() {}

func (*Envelope_PartyClose) isEnvelope_Message() {}

func (*Envelope_PartyJoinRequestList) isEnvelope_Message() {}

func (*Envelope_PartyJoinRequest) isEnvelope_Message() {}

func (*Envelope_PartyMatchmakerAdd) isEnvelope_Message() {}

func (*Envelope_PartyMatchmakerRemove) isEnvelope_Message() {}

func (*Envelope_PartyMatchmakerTicket) isEnvelope_Message() {}

func (*Envelope_PartyData) isEnvelope_Message() {}

func (*Envelope_PartyDataSend) isEnvelope_Message() {}

func (*Envelope_PartyPresenceEvent) isEnvelope_Message() {}

// A realtime chat channel.
type Channel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the channel.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The users currently in the channel.
	Presences []*UserPresence `protobuf:"bytes,2,rep,name=presences,proto3" json:"presences,omitempty"`
	// A reference to the current user's presence in the channel.
	Self *UserPresence `protobuf:"bytes,3,opt,name=self,proto3" json:"self,omitempty"`
	// The name of the chat room, or an empty string if this message was not sent through a chat room.
	RoomName string `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// The ID of the group, or an empty string if this message was not sent through a group channel.
	GroupId string `protobuf:"bytes,5,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// The ID of the first DM user, or an empty string if this message was not sent through a DM chat.
	UserIdOne string `protobuf:"bytes,6,opt,name=user_id_one,json=userIdOne,proto3" json:"user_id_one,omitempty"`
	// The ID of the second DM user, or an empty string if this message was not sent through a DM chat.
	UserIdTwo string `protobuf:"bytes,7,opt,name=user_id_two,json=userIdTwo,proto3" json:"user_id_two,omitempty"`
}

func (x *Channel) Reset() {
	*x = Channel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Channel) ProtoMessage() {}

func (x *Channel) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Channel.ProtoReflect.Descriptor instead.
func (*Channel) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{1}
}

func (x *Channel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Channel) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

func (x *Channel) GetSelf() *UserPresence {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *Channel) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *Channel) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *Channel) GetUserIdOne() string {
	if x != nil {
		return x.UserIdOne
	}
	return ""
}

func (x *Channel) GetUserIdTwo() string {
	if x != nil {
		return x.UserIdTwo
	}
	return ""
}

// Join operation for a realtime chat channel.
type ChannelJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user ID to DM with, group ID to chat with, or room channel name to join.
	Target string `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	// The type of the chat channel.
	Type int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"` // one of "ChannelId.Type".
	// Whether messages sent on this channel should be persistent.
	Persistence *wrapperspb.BoolValue `protobuf:"bytes,3,opt,name=persistence,proto3" json:"persistence,omitempty"`
	// Whether the user should appear in the channel's presence list and events.
	Hidden *wrapperspb.BoolValue `protobuf:"bytes,4,opt,name=hidden,proto3" json:"hidden,omitempty"`
}

func (x *ChannelJoin) Reset() {
	*x = ChannelJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelJoin) ProtoMessage() {}

func (x *ChannelJoin) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelJoin.ProtoReflect.Descriptor instead.
func (*ChannelJoin) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{2}
}

func (x *ChannelJoin) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *ChannelJoin) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ChannelJoin) GetPersistence() *wrapperspb.BoolValue {
	if x != nil {
		return x.Persistence
	}
	return nil
}

func (x *ChannelJoin) GetHidden() *wrapperspb.BoolValue {
	if x != nil {
		return x.Hidden
	}
	return nil
}

// Leave a realtime channel.
type ChannelLeave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ID of the channel to leave.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
}

func (x *ChannelLeave) Reset() {
	*x = ChannelLeave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelLeave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelLeave) ProtoMessage() {}

func (x *ChannelLeave) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelLeave.ProtoReflect.Descriptor instead.
func (*ChannelLeave) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{3}
}

func (x *ChannelLeave) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

// A receipt reply from a channel message send operation.
type ChannelMessageAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel the message was sent to.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// The unique ID assigned to the message.
	MessageId string `protobuf:"bytes,2,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// The code representing a message type or category.
	Code *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// Username of the message sender.
	Username string `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the message was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The UNIX time (for gRPC clients) or ISO string (for REST clients) when the message was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// True if the message was persisted to the channel's history, false otherwise.
	Persistent *wrapperspb.BoolValue `protobuf:"bytes,7,opt,name=persistent,proto3" json:"persistent,omitempty"`
	// The name of the chat room, or an empty string if this message was not sent through a chat room.
	RoomName string `protobuf:"bytes,8,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// The ID of the group, or an empty string if this message was not sent through a group channel.
	GroupId string `protobuf:"bytes,9,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// The ID of the first DM user, or an empty string if this message was not sent through a DM chat.
	UserIdOne string `protobuf:"bytes,10,opt,name=user_id_one,json=userIdOne,proto3" json:"user_id_one,omitempty"`
	// The ID of the second DM user, or an empty string if this message was not sent through a DM chat.
	UserIdTwo string `protobuf:"bytes,11,opt,name=user_id_two,json=userIdTwo,proto3" json:"user_id_two,omitempty"`
}

func (x *ChannelMessageAck) Reset() {
	*x = ChannelMessageAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelMessageAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelMessageAck) ProtoMessage() {}

func (x *ChannelMessageAck) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelMessageAck.ProtoReflect.Descriptor instead.
func (*ChannelMessageAck) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{4}
}

func (x *ChannelMessageAck) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelMessageAck) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ChannelMessageAck) GetCode() *wrapperspb.Int32Value {
	if x != nil {
		return x.Code
	}
	return nil
}

func (x *ChannelMessageAck) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChannelMessageAck) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ChannelMessageAck) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ChannelMessageAck) GetPersistent() *wrapperspb.BoolValue {
	if x != nil {
		return x.Persistent
	}
	return nil
}

func (x *ChannelMessageAck) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *ChannelMessageAck) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ChannelMessageAck) GetUserIdOne() string {
	if x != nil {
		return x.UserIdOne
	}
	return ""
}

func (x *ChannelMessageAck) GetUserIdTwo() string {
	if x != nil {
		return x.UserIdTwo
	}
	return ""
}

// Send a message to a realtime channel.
type ChannelMessageSend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel to sent to.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// Message content.
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ChannelMessageSend) Reset() {
	*x = ChannelMessageSend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelMessageSend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelMessageSend) ProtoMessage() {}

func (x *ChannelMessageSend) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelMessageSend.ProtoReflect.Descriptor instead.
func (*ChannelMessageSend) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{5}
}

func (x *ChannelMessageSend) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelMessageSend) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// Update a message previously sent to a realtime channel.
type ChannelMessageUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel the message was sent to.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// The ID assigned to the message to update.
	MessageId string `protobuf:"bytes,2,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// New message content.
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ChannelMessageUpdate) Reset() {
	*x = ChannelMessageUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelMessageUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelMessageUpdate) ProtoMessage() {}

func (x *ChannelMessageUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelMessageUpdate.ProtoReflect.Descriptor instead.
func (*ChannelMessageUpdate) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{6}
}

func (x *ChannelMessageUpdate) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelMessageUpdate) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ChannelMessageUpdate) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// Remove a message previously sent to a realtime channel.
type ChannelMessageRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel the message was sent to.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// The ID assigned to the message to update.
	MessageId string `protobuf:"bytes,2,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
}

func (x *ChannelMessageRemove) Reset() {
	*x = ChannelMessageRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelMessageRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelMessageRemove) ProtoMessage() {}

func (x *ChannelMessageRemove) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelMessageRemove.ProtoReflect.Descriptor instead.
func (*ChannelMessageRemove) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{7}
}

func (x *ChannelMessageRemove) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelMessageRemove) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

// A set of joins and leaves on a particular channel.
type ChannelPresenceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The channel identifier this event is for.
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// Presences joining the channel as part of this event, if any.
	Joins []*UserPresence `protobuf:"bytes,2,rep,name=joins,proto3" json:"joins,omitempty"`
	// Presences leaving the channel as part of this event, if any.
	Leaves []*UserPresence `protobuf:"bytes,3,rep,name=leaves,proto3" json:"leaves,omitempty"`
	// The name of the chat room, or an empty string if this message was not sent through a chat room.
	RoomName string `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	// The ID of the group, or an empty string if this message was not sent through a group channel.
	GroupId string `protobuf:"bytes,5,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// The ID of the first DM user, or an empty string if this message was not sent through a DM chat.
	UserIdOne string `protobuf:"bytes,6,opt,name=user_id_one,json=userIdOne,proto3" json:"user_id_one,omitempty"`
	// The ID of the second DM user, or an empty string if this message was not sent through a DM chat.
	UserIdTwo string `protobuf:"bytes,7,opt,name=user_id_two,json=userIdTwo,proto3" json:"user_id_two,omitempty"`
}

func (x *ChannelPresenceEvent) Reset() {
	*x = ChannelPresenceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelPresenceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelPresenceEvent) ProtoMessage() {}

func (x *ChannelPresenceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelPresenceEvent.ProtoReflect.Descriptor instead.
func (*ChannelPresenceEvent) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{8}
}

func (x *ChannelPresenceEvent) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelPresenceEvent) GetJoins() []*UserPresence {
	if x != nil {
		return x.Joins
	}
	return nil
}

func (x *ChannelPresenceEvent) GetLeaves() []*UserPresence {
	if x != nil {
		return x.Leaves
	}
	return nil
}

func (x *ChannelPresenceEvent) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *ChannelPresenceEvent) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ChannelPresenceEvent) GetUserIdOne() string {
	if x != nil {
		return x.UserIdOne
	}
	return ""
}

func (x *ChannelPresenceEvent) GetUserIdTwo() string {
	if x != nil {
		return x.UserIdTwo
	}
	return ""
}

// A logical error which may occur on the server.
type Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The error code which should be one of "Error.Code" enums.
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// A message in English to help developers debug the response.
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// Additional error details which may be different for each response.
	Context map[string]string `protobuf:"bytes,3,rep,name=context,proto3" json:"context,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Error) Reset() {
	*x = Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{9}
}

func (x *Error) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetContext() map[string]string {
	if x != nil {
		return x.Context
	}
	return nil
}

// A realtime match.
type Match struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
	// True if it's an server-managed authoritative match, false otherwise.
	Authoritative bool `protobuf:"varint,2,opt,name=authoritative,proto3" json:"authoritative,omitempty"`
	// Match label, if any.
	Label *wrapperspb.StringValue `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	// The number of users currently in the match.
	Size int32 `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	// The users currently in the match.
	Presences []*UserPresence `protobuf:"bytes,5,rep,name=presences,proto3" json:"presences,omitempty"`
	// A reference to the current user's presence in the match.
	Self *UserPresence `protobuf:"bytes,6,opt,name=self,proto3" json:"self,omitempty"`
}

func (x *Match) Reset() {
	*x = Match{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Match) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Match) ProtoMessage() {}

func (x *Match) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Match.ProtoReflect.Descriptor instead.
func (*Match) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{10}
}

func (x *Match) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *Match) GetAuthoritative() bool {
	if x != nil {
		return x.Authoritative
	}
	return false
}

func (x *Match) GetLabel() *wrapperspb.StringValue {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *Match) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Match) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

func (x *Match) GetSelf() *UserPresence {
	if x != nil {
		return x.Self
	}
	return nil
}

// Create a new realtime match.
type MatchCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional name to use when creating the match.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *MatchCreate) Reset() {
	*x = MatchCreate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchCreate) ProtoMessage() {}

func (x *MatchCreate) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchCreate.ProtoReflect.Descriptor instead.
func (*MatchCreate) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{11}
}

func (x *MatchCreate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Realtime match data received from the server.
type MatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
	// A reference to the user presence that sent this data, if any.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
	// Op code value.
	OpCode int64 `protobuf:"varint,3,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	// Data payload, if any.
	Data []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	// True if this data was delivered reliably, false otherwise.
	Reliable bool `protobuf:"varint,5,opt,name=reliable,proto3" json:"reliable,omitempty"`
}

func (x *MatchData) Reset() {
	*x = MatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchData) ProtoMessage() {}

func (x *MatchData) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchData.ProtoReflect.Descriptor instead.
func (*MatchData) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{12}
}

func (x *MatchData) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchData) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *MatchData) GetOpCode() int64 {
	if x != nil {
		return x.OpCode
	}
	return 0
}

func (x *MatchData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MatchData) GetReliable() bool {
	if x != nil {
		return x.Reliable
	}
	return false
}

// Send realtime match data to the server.
type MatchDataSend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
	// Op code value.
	OpCode int64 `protobuf:"varint,2,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	// Data payload, if any.
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// List of presences in the match to deliver to, if filtering is required. Otherwise deliver to everyone in the match.
	Presences []*UserPresence `protobuf:"bytes,4,rep,name=presences,proto3" json:"presences,omitempty"`
	// True if the data should be sent reliably, false otherwise.
	Reliable bool `protobuf:"varint,5,opt,name=reliable,proto3" json:"reliable,omitempty"`
}

func (x *MatchDataSend) Reset() {
	*x = MatchDataSend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchDataSend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchDataSend) ProtoMessage() {}

func (x *MatchDataSend) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchDataSend.ProtoReflect.Descriptor instead.
func (*MatchDataSend) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{13}
}

func (x *MatchDataSend) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchDataSend) GetOpCode() int64 {
	if x != nil {
		return x.OpCode
	}
	return 0
}

func (x *MatchDataSend) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MatchDataSend) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

func (x *MatchDataSend) GetReliable() bool {
	if x != nil {
		return x.Reliable
	}
	return false
}

// Join an existing realtime match.
type MatchJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Id:
	//
	//	*MatchJoin_MatchId
	//	*MatchJoin_Token
	Id isMatchJoin_Id `protobuf_oneof:"id"`
	// An optional set of key-value metadata pairs to be passed to the match handler, if any.
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MatchJoin) Reset() {
	*x = MatchJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchJoin) ProtoMessage() {}

func (x *MatchJoin) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchJoin.ProtoReflect.Descriptor instead.
func (*MatchJoin) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{14}
}

func (m *MatchJoin) GetId() isMatchJoin_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *MatchJoin) GetMatchId() string {
	if x, ok := x.GetId().(*MatchJoin_MatchId); ok {
		return x.MatchId
	}
	return ""
}

func (x *MatchJoin) GetToken() string {
	if x, ok := x.GetId().(*MatchJoin_Token); ok {
		return x.Token
	}
	return ""
}

func (x *MatchJoin) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type isMatchJoin_Id interface {
	isMatchJoin_Id()
}

type MatchJoin_MatchId struct {
	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3,oneof"`
}

type MatchJoin_Token struct {
	// A matchmaking result token.
	Token string `protobuf:"bytes,2,opt,name=token,proto3,oneof"`
}

func (*MatchJoin_MatchId) isMatchJoin_Id() {}

func (*MatchJoin_Token) isMatchJoin_Id() {}

// Leave a realtime match.
type MatchLeave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
}

func (x *MatchLeave) Reset() {
	*x = MatchLeave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchLeave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchLeave) ProtoMessage() {}

func (x *MatchLeave) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchLeave.ProtoReflect.Descriptor instead.
func (*MatchLeave) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{15}
}

func (x *MatchLeave) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

// A set of joins and leaves on a particular realtime match.
type MatchPresenceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The match unique ID.
	MatchId string `protobuf:"bytes,1,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`
	// User presences that have just joined the match.
	Joins []*UserPresence `protobuf:"bytes,2,rep,name=joins,proto3" json:"joins,omitempty"`
	// User presences that have just left the match.
	Leaves []*UserPresence `protobuf:"bytes,3,rep,name=leaves,proto3" json:"leaves,omitempty"`
}

func (x *MatchPresenceEvent) Reset() {
	*x = MatchPresenceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchPresenceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchPresenceEvent) ProtoMessage() {}

func (x *MatchPresenceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchPresenceEvent.ProtoReflect.Descriptor instead.
func (*MatchPresenceEvent) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{16}
}

func (x *MatchPresenceEvent) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchPresenceEvent) GetJoins() []*UserPresence {
	if x != nil {
		return x.Joins
	}
	return nil
}

func (x *MatchPresenceEvent) GetLeaves() []*UserPresence {
	if x != nil {
		return x.Leaves
	}
	return nil
}

// Start a new matchmaking process.
type MatchmakerAdd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Minimum total user count to match together.
	MinCount int32 `protobuf:"varint,1,opt,name=min_count,json=minCount,proto3" json:"min_count,omitempty"`
	// Maximum total user count to match together.
	MaxCount int32 `protobuf:"varint,2,opt,name=max_count,json=maxCount,proto3" json:"max_count,omitempty"`
	// Filter query used to identify suitable users.
	Query string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`
	// String properties.
	StringProperties map[string]string `protobuf:"bytes,4,rep,name=string_properties,json=stringProperties,proto3" json:"string_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Numeric properties.
	NumericProperties map[string]float64 `protobuf:"bytes,5,rep,name=numeric_properties,json=numericProperties,proto3" json:"numeric_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	// Optional multiple of the count that must be satisfied.
	CountMultiple *wrapperspb.Int32Value `protobuf:"bytes,6,opt,name=count_multiple,json=countMultiple,proto3" json:"count_multiple,omitempty"`
}

func (x *MatchmakerAdd) Reset() {
	*x = MatchmakerAdd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchmakerAdd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchmakerAdd) ProtoMessage() {}

func (x *MatchmakerAdd) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchmakerAdd.ProtoReflect.Descriptor instead.
func (*MatchmakerAdd) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{17}
}

func (x *MatchmakerAdd) GetMinCount() int32 {
	if x != nil {
		return x.MinCount
	}
	return 0
}

func (x *MatchmakerAdd) GetMaxCount() int32 {
	if x != nil {
		return x.MaxCount
	}
	return 0
}

func (x *MatchmakerAdd) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *MatchmakerAdd) GetStringProperties() map[string]string {
	if x != nil {
		return x.StringProperties
	}
	return nil
}

func (x *MatchmakerAdd) GetNumericProperties() map[string]float64 {
	if x != nil {
		return x.NumericProperties
	}
	return nil
}

func (x *MatchmakerAdd) GetCountMultiple() *wrapperspb.Int32Value {
	if x != nil {
		return x.CountMultiple
	}
	return nil
}

// A successful matchmaking result.
type MatchmakerMatched struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The matchmaking ticket that has completed.
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// The match token or match ID to join.
	//
	// Types that are assignable to Id:
	//
	//	*MatchmakerMatched_MatchId
	//	*MatchmakerMatched_Token
	Id isMatchmakerMatched_Id `protobuf_oneof:"id"`
	// The users that have been matched together, and information about their matchmaking data.
	Users []*MatchmakerMatched_MatchmakerUser `protobuf:"bytes,4,rep,name=users,proto3" json:"users,omitempty"`
	// A reference to the current user and their properties.
	Self *MatchmakerMatched_MatchmakerUser `protobuf:"bytes,5,opt,name=self,proto3" json:"self,omitempty"`
}

func (x *MatchmakerMatched) Reset() {
	*x = MatchmakerMatched{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchmakerMatched) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchmakerMatched) ProtoMessage() {}

func (x *MatchmakerMatched) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchmakerMatched.ProtoReflect.Descriptor instead.
func (*MatchmakerMatched) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{18}
}

func (x *MatchmakerMatched) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (m *MatchmakerMatched) GetId() isMatchmakerMatched_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *MatchmakerMatched) GetMatchId() string {
	if x, ok := x.GetId().(*MatchmakerMatched_MatchId); ok {
		return x.MatchId
	}
	return ""
}

func (x *MatchmakerMatched) GetToken() string {
	if x, ok := x.GetId().(*MatchmakerMatched_Token); ok {
		return x.Token
	}
	return ""
}

func (x *MatchmakerMatched) GetUsers() []*MatchmakerMatched_MatchmakerUser {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *MatchmakerMatched) GetSelf() *MatchmakerMatched_MatchmakerUser {
	if x != nil {
		return x.Self
	}
	return nil
}

type isMatchmakerMatched_Id interface {
	isMatchmakerMatched_Id()
}

type MatchmakerMatched_MatchId struct {
	// Match ID.
	MatchId string `protobuf:"bytes,2,opt,name=match_id,json=matchId,proto3,oneof"`
}

type MatchmakerMatched_Token struct {
	// Match join token.
	Token string `protobuf:"bytes,3,opt,name=token,proto3,oneof"`
}

func (*MatchmakerMatched_MatchId) isMatchmakerMatched_Id() {}

func (*MatchmakerMatched_Token) isMatchmakerMatched_Id() {}

// Cancel an existing ongoing matchmaking process.
type MatchmakerRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ticket to cancel.
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *MatchmakerRemove) Reset() {
	*x = MatchmakerRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchmakerRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchmakerRemove) ProtoMessage() {}

func (x *MatchmakerRemove) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchmakerRemove.ProtoReflect.Descriptor instead.
func (*MatchmakerRemove) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{19}
}

func (x *MatchmakerRemove) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

// A ticket representing a new matchmaking process.
type MatchmakerTicket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The ticket that can be used to cancel matchmaking.
	Ticket string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *MatchmakerTicket) Reset() {
	*x = MatchmakerTicket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchmakerTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchmakerTicket) ProtoMessage() {}

func (x *MatchmakerTicket) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchmakerTicket.ProtoReflect.Descriptor instead.
func (*MatchmakerTicket) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{20}
}

func (x *MatchmakerTicket) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

// A collection of zero or more notifications.
type Notifications struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Collection of notifications.
	Notifications []*api.Notification `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
}

func (x *Notifications) Reset() {
	*x = Notifications{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Notifications) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notifications) ProtoMessage() {}

func (x *Notifications) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notifications.ProtoReflect.Descriptor instead.
func (*Notifications) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{21}
}

func (x *Notifications) GetNotifications() []*api.Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

// Incoming information about a party.
type Party struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique party identifier.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// Open flag.
	Open bool `protobuf:"varint,2,opt,name=open,proto3" json:"open,omitempty"`
	// Maximum number of party members.
	MaxSize int32 `protobuf:"varint,3,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
	// Self.
	Self *UserPresence `protobuf:"bytes,4,opt,name=self,proto3" json:"self,omitempty"`
	// Leader.
	Leader *UserPresence `protobuf:"bytes,5,opt,name=leader,proto3" json:"leader,omitempty"`
	// All current party members.
	Presences []*UserPresence `protobuf:"bytes,6,rep,name=presences,proto3" json:"presences,omitempty"`
}

func (x *Party) Reset() {
	*x = Party{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Party) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Party) ProtoMessage() {}

func (x *Party) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Party.ProtoReflect.Descriptor instead.
func (*Party) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{22}
}

func (x *Party) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *Party) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

func (x *Party) GetMaxSize() int32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

func (x *Party) GetSelf() *UserPresence {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *Party) GetLeader() *UserPresence {
	if x != nil {
		return x.Leader
	}
	return nil
}

func (x *Party) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

// Create a party.
type PartyCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether or not the party will require join requests to be approved by the party leader.
	Open bool `protobuf:"varint,1,opt,name=open,proto3" json:"open,omitempty"`
	// Maximum number of party members.
	MaxSize int32 `protobuf:"varint,2,opt,name=max_size,json=maxSize,proto3" json:"max_size,omitempty"`
}

func (x *PartyCreate) Reset() {
	*x = PartyCreate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyCreate) ProtoMessage() {}

func (x *PartyCreate) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyCreate.ProtoReflect.Descriptor instead.
func (*PartyCreate) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{23}
}

func (x *PartyCreate) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

func (x *PartyCreate) GetMaxSize() int32 {
	if x != nil {
		return x.MaxSize
	}
	return 0
}

// Join a party, or request to join if the party is not open.
type PartyJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to join.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
}

func (x *PartyJoin) Reset() {
	*x = PartyJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyJoin) ProtoMessage() {}

func (x *PartyJoin) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyJoin.ProtoReflect.Descriptor instead.
func (*PartyJoin) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{24}
}

func (x *PartyJoin) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

// Leave a party.
type PartyLeave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to leave.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
}

func (x *PartyLeave) Reset() {
	*x = PartyLeave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyLeave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyLeave) ProtoMessage() {}

func (x *PartyLeave) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyLeave.ProtoReflect.Descriptor instead.
func (*PartyLeave) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{25}
}

func (x *PartyLeave) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

// Promote a new party leader.
type PartyPromote struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to promote a new leader for.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The presence of an existing party member to promote as the new leader.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
}

func (x *PartyPromote) Reset() {
	*x = PartyPromote{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyPromote) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyPromote) ProtoMessage() {}

func (x *PartyPromote) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyPromote.ProtoReflect.Descriptor instead.
func (*PartyPromote) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{26}
}

func (x *PartyPromote) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyPromote) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

// Announcement of a new party leader.
type PartyLeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to announce the new leader for.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The presence of the new party leader.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
}

func (x *PartyLeader) Reset() {
	*x = PartyLeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyLeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyLeader) ProtoMessage() {}

func (x *PartyLeader) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyLeader.ProtoReflect.Descriptor instead.
func (*PartyLeader) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{27}
}

func (x *PartyLeader) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyLeader) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

// Accept a request to join.
type PartyAccept struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to accept a join request for.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The presence to accept as a party member.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
}

func (x *PartyAccept) Reset() {
	*x = PartyAccept{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyAccept) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyAccept) ProtoMessage() {}

func (x *PartyAccept) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyAccept.ProtoReflect.Descriptor instead.
func (*PartyAccept) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{28}
}

func (x *PartyAccept) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyAccept) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

// Kick a party member, or decline a request to join.
type PartyRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to remove/reject from.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The presence to remove or reject.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
}

func (x *PartyRemove) Reset() {
	*x = PartyRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyRemove) ProtoMessage() {}

func (x *PartyRemove) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyRemove.ProtoReflect.Descriptor instead.
func (*PartyRemove) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{29}
}

func (x *PartyRemove) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyRemove) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

// End a party, kicking all party members and closing it.
type PartyClose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to close.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
}

func (x *PartyClose) Reset() {
	*x = PartyClose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyClose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyClose) ProtoMessage() {}

func (x *PartyClose) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyClose.ProtoReflect.Descriptor instead.
func (*PartyClose) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{30}
}

func (x *PartyClose) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

// Request a list of pending join requests for a party.
type PartyJoinRequestList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to get a list of join requests for.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
}

func (x *PartyJoinRequestList) Reset() {
	*x = PartyJoinRequestList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyJoinRequestList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyJoinRequestList) ProtoMessage() {}

func (x *PartyJoinRequestList) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyJoinRequestList.ProtoReflect.Descriptor instead.
func (*PartyJoinRequestList) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{31}
}

func (x *PartyJoinRequestList) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

// Incoming notification for one or more new presences attempting to join the party.
type PartyJoinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID these presences are attempting to join.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// Presences attempting to join.
	Presences []*UserPresence `protobuf:"bytes,2,rep,name=presences,proto3" json:"presences,omitempty"`
}

func (x *PartyJoinRequest) Reset() {
	*x = PartyJoinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyJoinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyJoinRequest) ProtoMessage() {}

func (x *PartyJoinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyJoinRequest.ProtoReflect.Descriptor instead.
func (*PartyJoinRequest) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{32}
}

func (x *PartyJoinRequest) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyJoinRequest) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

// Begin matchmaking as a party.
type PartyMatchmakerAdd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// Minimum total user count to match together.
	MinCount int32 `protobuf:"varint,2,opt,name=min_count,json=minCount,proto3" json:"min_count,omitempty"`
	// Maximum total user count to match together.
	MaxCount int32 `protobuf:"varint,3,opt,name=max_count,json=maxCount,proto3" json:"max_count,omitempty"`
	// Filter query used to identify suitable users.
	Query string `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	// String properties.
	StringProperties map[string]string `protobuf:"bytes,5,rep,name=string_properties,json=stringProperties,proto3" json:"string_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Numeric properties.
	NumericProperties map[string]float64 `protobuf:"bytes,6,rep,name=numeric_properties,json=numericProperties,proto3" json:"numeric_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	// Optional multiple of the count that must be satisfied.
	CountMultiple *wrapperspb.Int32Value `protobuf:"bytes,7,opt,name=count_multiple,json=countMultiple,proto3" json:"count_multiple,omitempty"`
}

func (x *PartyMatchmakerAdd) Reset() {
	*x = PartyMatchmakerAdd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyMatchmakerAdd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyMatchmakerAdd) ProtoMessage() {}

func (x *PartyMatchmakerAdd) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyMatchmakerAdd.ProtoReflect.Descriptor instead.
func (*PartyMatchmakerAdd) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{33}
}

func (x *PartyMatchmakerAdd) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyMatchmakerAdd) GetMinCount() int32 {
	if x != nil {
		return x.MinCount
	}
	return 0
}

func (x *PartyMatchmakerAdd) GetMaxCount() int32 {
	if x != nil {
		return x.MaxCount
	}
	return 0
}

func (x *PartyMatchmakerAdd) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *PartyMatchmakerAdd) GetStringProperties() map[string]string {
	if x != nil {
		return x.StringProperties
	}
	return nil
}

func (x *PartyMatchmakerAdd) GetNumericProperties() map[string]float64 {
	if x != nil {
		return x.NumericProperties
	}
	return nil
}

func (x *PartyMatchmakerAdd) GetCountMultiple() *wrapperspb.Int32Value {
	if x != nil {
		return x.CountMultiple
	}
	return nil
}

// Cancel a party matchmaking process using a ticket.
type PartyMatchmakerRemove struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The ticket to cancel.
	Ticket string `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *PartyMatchmakerRemove) Reset() {
	*x = PartyMatchmakerRemove{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyMatchmakerRemove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyMatchmakerRemove) ProtoMessage() {}

func (x *PartyMatchmakerRemove) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyMatchmakerRemove.ProtoReflect.Descriptor instead.
func (*PartyMatchmakerRemove) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{34}
}

func (x *PartyMatchmakerRemove) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyMatchmakerRemove) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

// A response from starting a new party matchmaking process.
type PartyMatchmakerTicket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// The ticket that can be used to cancel matchmaking.
	Ticket string `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *PartyMatchmakerTicket) Reset() {
	*x = PartyMatchmakerTicket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyMatchmakerTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyMatchmakerTicket) ProtoMessage() {}

func (x *PartyMatchmakerTicket) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyMatchmakerTicket.ProtoReflect.Descriptor instead.
func (*PartyMatchmakerTicket) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{35}
}

func (x *PartyMatchmakerTicket) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyMatchmakerTicket) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

// Incoming party data delivered from the server.
type PartyData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The party ID.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// A reference to the user presence that sent this data, if any.
	Presence *UserPresence `protobuf:"bytes,2,opt,name=presence,proto3" json:"presence,omitempty"`
	// Op code value.
	OpCode int64 `protobuf:"varint,3,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	// Data payload, if any.
	Data []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PartyData) Reset() {
	*x = PartyData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyData) ProtoMessage() {}

func (x *PartyData) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyData.ProtoReflect.Descriptor instead.
func (*PartyData) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{36}
}

func (x *PartyData) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyData) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *PartyData) GetOpCode() int64 {
	if x != nil {
		return x.OpCode
	}
	return 0
}

func (x *PartyData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// Send data to a party.
type PartyDataSend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Party ID to send to.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// Op code value.
	OpCode int64 `protobuf:"varint,2,opt,name=op_code,json=opCode,proto3" json:"op_code,omitempty"`
	// Data payload, if any.
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *PartyDataSend) Reset() {
	*x = PartyDataSend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyDataSend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyDataSend) ProtoMessage() {}

func (x *PartyDataSend) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyDataSend.ProtoReflect.Descriptor instead.
func (*PartyDataSend) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{37}
}

func (x *PartyDataSend) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyDataSend) GetOpCode() int64 {
	if x != nil {
		return x.OpCode
	}
	return 0
}

func (x *PartyDataSend) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// Presence update for a particular party.
type PartyPresenceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The party ID.
	PartyId string `protobuf:"bytes,1,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// User presences that have just joined the party.
	Joins []*UserPresence `protobuf:"bytes,2,rep,name=joins,proto3" json:"joins,omitempty"`
	// User presences that have just left the party.
	Leaves []*UserPresence `protobuf:"bytes,3,rep,name=leaves,proto3" json:"leaves,omitempty"`
}

func (x *PartyPresenceEvent) Reset() {
	*x = PartyPresenceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PartyPresenceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyPresenceEvent) ProtoMessage() {}

func (x *PartyPresenceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyPresenceEvent.ProtoReflect.Descriptor instead.
func (*PartyPresenceEvent) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{38}
}

func (x *PartyPresenceEvent) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *PartyPresenceEvent) GetJoins() []*UserPresence {
	if x != nil {
		return x.Joins
	}
	return nil
}

func (x *PartyPresenceEvent) GetLeaves() []*UserPresence {
	if x != nil {
		return x.Leaves
	}
	return nil
}

// Application-level heartbeat and connection check.
type Ping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Ping) Reset() {
	*x = Ping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ping) ProtoMessage() {}

func (x *Ping) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ping.ProtoReflect.Descriptor instead.
func (*Ping) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{39}
}

// Application-level heartbeat and connection check response.
type Pong struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Pong) Reset() {
	*x = Pong{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pong) ProtoMessage() {}

func (x *Pong) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pong.ProtoReflect.Descriptor instead.
func (*Pong) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{40}
}

// A snapshot of statuses for some set of users.
type Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User statuses.
	Presences []*UserPresence `protobuf:"bytes,1,rep,name=presences,proto3" json:"presences,omitempty"`
}

func (x *Status) Reset() {
	*x = Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status) ProtoMessage() {}

func (x *Status) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status.ProtoReflect.Descriptor instead.
func (*Status) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{41}
}

func (x *Status) GetPresences() []*UserPresence {
	if x != nil {
		return x.Presences
	}
	return nil
}

// Start receiving status updates for some set of users.
type StatusFollow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User IDs to follow.
	UserIds []string `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
	// Usernames to follow.
	Usernames []string `protobuf:"bytes,2,rep,name=usernames,proto3" json:"usernames,omitempty"`
}

func (x *StatusFollow) Reset() {
	*x = StatusFollow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusFollow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusFollow) ProtoMessage() {}

func (x *StatusFollow) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusFollow.ProtoReflect.Descriptor instead.
func (*StatusFollow) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{42}
}

func (x *StatusFollow) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *StatusFollow) GetUsernames() []string {
	if x != nil {
		return x.Usernames
	}
	return nil
}

// A batch of status updates for a given user.
type StatusPresenceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// New statuses for the user.
	Joins []*UserPresence `protobuf:"bytes,2,rep,name=joins,proto3" json:"joins,omitempty"`
	// Previous statuses for the user.
	Leaves []*UserPresence `protobuf:"bytes,3,rep,name=leaves,proto3" json:"leaves,omitempty"`
}

func (x *StatusPresenceEvent) Reset() {
	*x = StatusPresenceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusPresenceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusPresenceEvent) ProtoMessage() {}

func (x *StatusPresenceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusPresenceEvent.ProtoReflect.Descriptor instead.
func (*StatusPresenceEvent) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{43}
}

func (x *StatusPresenceEvent) GetJoins() []*UserPresence {
	if x != nil {
		return x.Joins
	}
	return nil
}

func (x *StatusPresenceEvent) GetLeaves() []*UserPresence {
	if x != nil {
		return x.Leaves
	}
	return nil
}

// Stop receiving status updates for some set of users.
type StatusUnfollow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Users to unfollow.
	UserIds []string `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"`
}

func (x *StatusUnfollow) Reset() {
	*x = StatusUnfollow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusUnfollow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusUnfollow) ProtoMessage() {}

func (x *StatusUnfollow) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusUnfollow.ProtoReflect.Descriptor instead.
func (*StatusUnfollow) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{44}
}

func (x *StatusUnfollow) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

// Set the user's own status.
type StatusUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status string to set, if not present the user will appear offline.
	Status *wrapperspb.StringValue `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StatusUpdate) Reset() {
	*x = StatusUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusUpdate) ProtoMessage() {}

func (x *StatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusUpdate.ProtoReflect.Descriptor instead.
func (*StatusUpdate) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{45}
}

func (x *StatusUpdate) GetStatus() *wrapperspb.StringValue {
	if x != nil {
		return x.Status
	}
	return nil
}

// Represents identifying information for a stream.
type Stream struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mode identifies the type of stream.
	Mode int32 `protobuf:"varint,1,opt,name=mode,proto3" json:"mode,omitempty"`
	// Subject is the primary identifier, if any.
	Subject string `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	// Subcontext is a secondary identifier, if any.
	Subcontext string `protobuf:"bytes,3,opt,name=subcontext,proto3" json:"subcontext,omitempty"`
	// The label is an arbitrary identifying string, if the stream has one.
	Label string `protobuf:"bytes,4,opt,name=label,proto3" json:"label,omitempty"`
}

func (x *Stream) Reset() {
	*x = Stream{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stream) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stream) ProtoMessage() {}

func (x *Stream) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stream.ProtoReflect.Descriptor instead.
func (*Stream) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{46}
}

func (x *Stream) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *Stream) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *Stream) GetSubcontext() string {
	if x != nil {
		return x.Subcontext
	}
	return ""
}

func (x *Stream) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

// A data message delivered over a stream.
type StreamData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The stream this data message relates to.
	Stream *Stream `protobuf:"bytes,1,opt,name=stream,proto3" json:"stream,omitempty"`
	// The sender, if any.
	Sender *UserPresence `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	// Arbitrary contents of the data message.
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// True if this data was delivered reliably, false otherwise.
	Reliable bool `protobuf:"varint,4,opt,name=reliable,proto3" json:"reliable,omitempty"`
}

func (x *StreamData) Reset() {
	*x = StreamData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamData) ProtoMessage() {}

func (x *StreamData) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamData.ProtoReflect.Descriptor instead.
func (*StreamData) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{47}
}

func (x *StreamData) GetStream() *Stream {
	if x != nil {
		return x.Stream
	}
	return nil
}

func (x *StreamData) GetSender() *UserPresence {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *StreamData) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *StreamData) GetReliable() bool {
	if x != nil {
		return x.Reliable
	}
	return false
}

// A set of joins and leaves on a particular stream.
type StreamPresenceEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The stream this event relates to.
	Stream *Stream `protobuf:"bytes,1,opt,name=stream,proto3" json:"stream,omitempty"`
	// Presences joining the stream as part of this event, if any.
	Joins []*UserPresence `protobuf:"bytes,2,rep,name=joins,proto3" json:"joins,omitempty"`
	// Presences leaving the stream as part of this event, if any.
	Leaves []*UserPresence `protobuf:"bytes,3,rep,name=leaves,proto3" json:"leaves,omitempty"`
}

func (x *StreamPresenceEvent) Reset() {
	*x = StreamPresenceEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamPresenceEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamPresenceEvent) ProtoMessage() {}

func (x *StreamPresenceEvent) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamPresenceEvent.ProtoReflect.Descriptor instead.
func (*StreamPresenceEvent) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{48}
}

func (x *StreamPresenceEvent) GetStream() *Stream {
	if x != nil {
		return x.Stream
	}
	return nil
}

func (x *StreamPresenceEvent) GetJoins() []*UserPresence {
	if x != nil {
		return x.Joins
	}
	return nil
}

func (x *StreamPresenceEvent) GetLeaves() []*UserPresence {
	if x != nil {
		return x.Leaves
	}
	return nil
}

// A user session associated to a stream, usually through a list operation or a join/leave event.
type UserPresence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The user this presence belongs to.
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// A unique session ID identifying the particular connection, because the user may have many.
	SessionId string `protobuf:"bytes,2,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	// The username for display purposes.
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	// Whether this presence generates persistent data/messages, if applicable for the stream type.
	Persistence bool `protobuf:"varint,4,opt,name=persistence,proto3" json:"persistence,omitempty"`
	// A user-set status message for this stream, if applicable.
	Status *wrapperspb.StringValue `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UserPresence) Reset() {
	*x = UserPresence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPresence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPresence) ProtoMessage() {}

func (x *UserPresence) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPresence.ProtoReflect.Descriptor instead.
func (*UserPresence) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{49}
}

func (x *UserPresence) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserPresence) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UserPresence) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserPresence) GetPersistence() bool {
	if x != nil {
		return x.Persistence
	}
	return false
}

func (x *UserPresence) GetStatus() *wrapperspb.StringValue {
	if x != nil {
		return x.Status
	}
	return nil
}

type MatchmakerMatched_MatchmakerUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User info.
	Presence *UserPresence `protobuf:"bytes,1,opt,name=presence,proto3" json:"presence,omitempty"`
	// Party identifier, if this user was matched as a party member.
	PartyId string `protobuf:"bytes,2,opt,name=party_id,json=partyId,proto3" json:"party_id,omitempty"`
	// String properties.
	StringProperties map[string]string `protobuf:"bytes,5,rep,name=string_properties,json=stringProperties,proto3" json:"string_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Numeric properties.
	NumericProperties map[string]float64 `protobuf:"bytes,6,rep,name=numeric_properties,json=numericProperties,proto3" json:"numeric_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
}

func (x *MatchmakerMatched_MatchmakerUser) Reset() {
	*x = MatchmakerMatched_MatchmakerUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_realtime_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchmakerMatched_MatchmakerUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchmakerMatched_MatchmakerUser) ProtoMessage() {}

func (x *MatchmakerMatched_MatchmakerUser) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchmakerMatched_MatchmakerUser.ProtoReflect.Descriptor instead.
func (*MatchmakerMatched_MatchmakerUser) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{18, 0}
}

func (x *MatchmakerMatched_MatchmakerUser) GetPresence() *UserPresence {
	if x != nil {
		return x.Presence
	}
	return nil
}

func (x *MatchmakerMatched_MatchmakerUser) GetPartyId() string {
	if x != nil {
		return x.PartyId
	}
	return ""
}

func (x *MatchmakerMatched_MatchmakerUser) GetStringProperties() map[string]string {
	if x != nil {
		return x.StringProperties
	}
	return nil
}

func (x *MatchmakerMatched_MatchmakerUser) GetNumericProperties() map[string]float64 {
	if x != nil {
		return x.NumericProperties
	}
	return nil
}

var File_realtime_proto protoreflect.FileDescriptor

var file_realtime_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0f, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xf4, 0x1b, 0x0a, 0x08, 0x45, 0x6e, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x12, 0x34, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x41, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4a, 0x6f, 0x69, 0x6e, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x44, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x48,
	0x00, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12,
	0x45, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x54, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x41, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x63, 0x6b, 0x12, 0x57, 0x0a, 0x14,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x48,
	0x00, 0x52, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x5d, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x14,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x00, 0x52, 0x14, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x6d,
	0x6f, 0x76, 0x65, 0x12, 0x5d, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x14, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x48, 0x00, 0x52, 0x05, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x41, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x48, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x73, 0x65, 0x6e, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x00, 0x52, 0x0d, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x3b, 0x0a, 0x0a,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x48, 0x00, 0x52, 0x09,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x57, 0x0a, 0x14, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x12,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x47, 0x0a, 0x0e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x61, 0x64, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x48, 0x00, 0x52, 0x0d, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x12, 0x53, 0x0a, 0x12, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d,
	0x61, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x48, 0x00, 0x52, 0x11, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x12, 0x50, 0x0a, 0x11, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x00,
	0x52, 0x10, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x12, 0x50, 0x0a, 0x11, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x48, 0x00, 0x52, 0x10, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x46, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x03,
	0x72, 0x70, 0x63, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x70, 0x63, 0x48, 0x00, 0x52, 0x03, 0x72, 0x70,
	0x63, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x5a, 0x0a, 0x15, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x75, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x12, 0x44, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5a, 0x0a, 0x15, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52,
	0x13, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x6e,
	0x67, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x50, 0x6f, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x12, 0x2e,
	0x0a, 0x05, 0x70, 0x61, 0x72, 0x74, 0x79, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x48, 0x00, 0x52, 0x05, 0x70, 0x61, 0x72, 0x74, 0x79, 0x12, 0x41,
	0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x3b, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69,
	0x6e, 0x48, 0x00, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x3e,
	0x0a, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x25, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x44,
	0x0a, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74,
	0x79, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x41, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x74, 0x79, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x48, 0x00,
	0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x3e, 0x0a,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x48,
	0x00, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x5e, 0x0a,
	0x17, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x14, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x51, 0x0a,
	0x12, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74,
	0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x10,
	0x70, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x57, 0x0a, 0x14, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d,
	0x61, 0x6b, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x48, 0x00, 0x52, 0x12, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x12, 0x60, 0x0a, 0x17, 0x70, 0x61, 0x72,
	0x74, 0x79, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f, 0x72, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x48, 0x00, 0x52, 0x15, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x60, 0x0a, 0x17, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50,
	0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x48, 0x00, 0x52, 0x15, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x3b, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x30, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x09, 0x70, 0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x0f, 0x70, 0x61,
	0x72, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x18, 0x31, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x65, 0x6e, 0x64, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x65, 0x6e, 0x64, 0x12, 0x57, 0x0a, 0x14, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x32, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x12, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x09, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x81, 0x02, 0x0a, 0x07, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x12, 0x31, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x04,
	0x73, 0x65, 0x6c, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x4f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x77, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x54, 0x77, 0x6f, 0x22, 0xf2, 0x01, 0x0a,
	0x0b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x06, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x22, 0x45, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x4f, 0x4f, 0x4d,
	0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10,
	0x03, 0x22, 0x2d, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x65, 0x61, 0x76,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64,
	0x22, 0xcc, 0x03, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x41, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x70, 0x65, 0x72,
	0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x4f, 0x6e, 0x65, 0x12,
	0x1e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x77, 0x6f, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x54, 0x77, 0x6f, 0x22,
	0x4d, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x6e,
	0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x54,
	0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x22, 0x99, 0x02, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05,
	0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6a, 0x6f, 0x69, 0x6e,
	0x73, 0x12, 0x35, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6f,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x4f, 0x6e, 0x65,
	0x12, 0x1e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x77, 0x6f, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x54, 0x77, 0x6f,
	0x22, 0xfc, 0x02, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x3a, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xc9, 0x01, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4e, 0x52, 0x45, 0x43, 0x4f, 0x47, 0x4e, 0x49,
	0x5a, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x41, 0x44, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10,
	0x03, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x4a, 0x4f, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x05, 0x12,
	0x1e, 0x0a, 0x1a, 0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x06, 0x12,
	0x1e, 0x0a, 0x1a, 0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x22,
	0x80, 0x02, 0x0a, 0x05, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x74, 0x61, 0x74, 0x69, 0x76, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x31, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x73, 0x65,
	0x6c, 0x66, 0x22, 0x21, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x70, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x0d, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x53, 0x65, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x09,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x6c,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4a,
	0x6f, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x44, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x4a, 0x6f, 0x69, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69,
	0x64, 0x22, 0x27, 0x0a, 0x0a, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x12, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05,
	0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6a, 0x6f, 0x69, 0x6e,
	0x73, 0x12, 0x35, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x22, 0xf7, 0x03, 0x0a, 0x0d, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d,
	0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x64, 0x0a,
	0x12, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x2e, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69,
	0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x11, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x1a, 0x43, 0x0a, 0x15, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16,
	0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xd9, 0x05, 0x0a, 0x11, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x1b, 0x0a, 0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x47, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61,
	0x6b, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x45,
	0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x73, 0x65, 0x6c, 0x66, 0x1a, 0xe0, 0x03, 0x0a, 0x0e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d,
	0x61, 0x6b, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b,
	0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x74,
	0x0a, 0x11, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x2e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x10, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x77, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x48, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x64, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x2e, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x65,
	0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x43, 0x0a,
	0x15, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0x2a,
	0x0a, 0x10, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x2a, 0x0a, 0x10, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x4f, 0x0a, 0x0d, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x05, 0x50, 0x61, 0x72, 0x74,
	0x79, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x73,
	0x65, 0x6c, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x12, 0x35,
	0x0a, 0x06, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x6c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x22, 0x3c, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x26, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x74,
	0x79, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49,
	0x64, 0x22, 0x64, 0x0a, 0x0c, 0x50, 0x61, 0x72, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x08,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x63, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x79,
	0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49,
	0x64, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x63, 0x0a, 0x0b,
	0x50, 0x61, 0x72, 0x74, 0x79, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d,
	0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x22, 0x63, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x79, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x08, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x27, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x74, 0x79, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x22,
	0x31, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x49, 0x64, 0x22, 0x6a, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x09, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0xa1,
	0x04, 0x0a, 0x12, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x6d, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x6d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x66, 0x0a, 0x11, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61,
	0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x69, 0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x65,
	0x72, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x6d, 0x61, 0x6b, 0x65, 0x72, 0x41, 0x64, 0x64, 0x2e, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69,
	0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x11, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x1a, 0x43, 0x0a, 0x15, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16,
	0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x4a, 0x0a, 0x15, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x6d, 0x61, 0x6b, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x4a,
	0x0a, 0x15, 0x50, 0x61, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x6d, 0x61, 0x6b, 0x65,
	0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x09, 0x50,
	0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x72, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x08, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x0d, 0x50,
	0x61, 0x72, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x9b, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x72, 0x74, 0x79, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x61, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x6c,
	0x65, 0x61, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61,
	0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x6c, 0x65, 0x61, 0x76,
	0x65, 0x73, 0x22, 0x06, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x22, 0x06, 0x0a, 0x04, 0x50, 0x6f,
	0x6e, 0x67, 0x22, 0x45, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x09,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09,
	0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x0c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x05, 0x6a, 0x6f,
	0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61,
	0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x12,
	0x35, 0x0a, 0x06, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06,
	0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x22, 0x2b, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x22, 0x44, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6c, 0x0a, 0x06, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0xa4, 0x01, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52,
	0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x35, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61,
	0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xb2,
	0x01, 0x0a, 0x13, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52,
	0x06, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x33, 0x0a, 0x05, 0x6a, 0x6f, 0x69, 0x6e, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e,
	0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6a, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x06,
	0x6c, 0x65, 0x61, 0x76, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x06, 0x6c, 0x65, 0x61,
	0x76, 0x65, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x70,
	0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x6c, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x69, 0x63, 0x6c, 0x61,
	0x62, 0x73, 0x2e, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x72, 0x74, 0x61, 0x70, 0x69, 0x42,
	0x0e, 0x4e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x50,
	0x01, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x65,
	0x72, 0x6f, 0x69, 0x63, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x6e, 0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x74, 0x61, 0x70, 0x69, 0xaa, 0x02, 0x0f, 0x4e,
	0x61, 0x6b, 0x61, 0x6d, 0x61, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_realtime_proto_rawDescOnce sync.Once
	file_realtime_proto_rawDescData = file_realtime_proto_rawDesc
)

func file_realtime_proto_rawDescGZIP() []byte {
	file_realtime_proto_rawDescOnce.Do(func() {
		file_realtime_proto_rawDescData = protoimpl.X.CompressGZIP(file_realtime_proto_rawDescData)
	})
	return file_realtime_proto_rawDescData
}

var file_realtime_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_realtime_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_realtime_proto_goTypes = []interface{}{
	(ChannelJoin_Type)(0),                    // 0: nakama.realtime.ChannelJoin.Type
	(Error_Code)(0),                          // 1: nakama.realtime.Error.Code
	(*Envelope)(nil),                         // 2: nakama.realtime.Envelope
	(*Channel)(nil),                          // 3: nakama.realtime.Channel
	(*ChannelJoin)(nil),                      // 4: nakama.realtime.ChannelJoin
	(*ChannelLeave)(nil),                     // 5: nakama.realtime.ChannelLeave
	(*ChannelMessageAck)(nil),                // 6: nakama.realtime.ChannelMessageAck
	(*ChannelMessageSend)(nil),               // 7: nakama.realtime.ChannelMessageSend
	(*ChannelMessageUpdate)(nil),             // 8: nakama.realtime.ChannelMessageUpdate
	(*ChannelMessageRemove)(nil),             // 9: nakama.realtime.ChannelMessageRemove
	(*ChannelPresenceEvent)(nil),             // 10: nakama.realtime.ChannelPresenceEvent
	(*Error)(nil),                            // 11: nakama.realtime.Error
	(*Match)(nil),                            // 12: nakama.realtime.Match
	(*MatchCreate)(nil),                      // 13: nakama.realtime.MatchCreate
	(*MatchData)(nil),                        // 14: nakama.realtime.MatchData
	(*MatchDataSend)(nil),                    // 15: nakama.realtime.MatchDataSend
	(*MatchJoin)(nil),                        // 16: nakama.realtime.MatchJoin
	(*MatchLeave)(nil),                       // 17: nakama.realtime.MatchLeave
	(*MatchPresenceEvent)(nil),               // 18: nakama.realtime.MatchPresenceEvent
	(*MatchmakerAdd)(nil),                    // 19: nakama.realtime.MatchmakerAdd
	(*MatchmakerMatched)(nil),                // 20: nakama.realtime.MatchmakerMatched
	(*MatchmakerRemove)(nil),                 // 21: nakama.realtime.MatchmakerRemove
	(*MatchmakerTicket)(nil),                 // 22: nakama.realtime.MatchmakerTicket
	(*Notifications)(nil),                    // 23: nakama.realtime.Notifications
	(*Party)(nil),                            // 24: nakama.realtime.Party
	(*PartyCreate)(nil),                      // 25: nakama.realtime.PartyCreate
	(*PartyJoin)(nil),                        // 26: nakama.realtime.PartyJoin
	(*PartyLeave)(nil),                       // 27: nakama.realtime.PartyLeave
	(*PartyPromote)(nil),                     // 28: nakama.realtime.PartyPromote
	(*PartyLeader)(nil),                      // 29: nakama.realtime.PartyLeader
	(*PartyAccept)(nil),                      // 30: nakama.realtime.PartyAccept
	(*PartyRemove)(nil),                      // 31: nakama.realtime.PartyRemove
	(*PartyClose)(nil),                       // 32: nakama.realtime.PartyClose
	(*PartyJoinRequestList)(nil),             // 33: nakama.realtime.PartyJoinRequestList
	(*PartyJoinRequest)(nil),                 // 34: nakama.realtime.PartyJoinRequest
	(*PartyMatchmakerAdd)(nil),               // 35: nakama.realtime.PartyMatchmakerAdd
	(*PartyMatchmakerRemove)(nil),            // 36: nakama.realtime.PartyMatchmakerRemove
	(*PartyMatchmakerTicket)(nil),            // 37: nakama.realtime.PartyMatchmakerTicket
	(*PartyData)(nil),                        // 38: nakama.realtime.PartyData
	(*PartyDataSend)(nil),                    // 39: nakama.realtime.PartyDataSend
	(*PartyPresenceEvent)(nil),               // 40: nakama.realtime.PartyPresenceEvent
	(*Ping)(nil),                             // 41: nakama.realtime.Ping
	(*Pong)(nil),                             // 42: nakama.realtime.Pong
	(*Status)(nil),                           // 43: nakama.realtime.Status
	(*StatusFollow)(nil),                     // 44: nakama.realtime.StatusFollow
	(*StatusPresenceEvent)(nil),              // 45: nakama.realtime.StatusPresenceEvent
	(*StatusUnfollow)(nil),                   // 46: nakama.realtime.StatusUnfollow
	(*StatusUpdate)(nil),                     // 47: nakama.realtime.StatusUpdate
	(*Stream)(nil),                           // 48: nakama.realtime.Stream
	(*StreamData)(nil),                       // 49: nakama.realtime.StreamData
	(*StreamPresenceEvent)(nil),              // 50: nakama.realtime.StreamPresenceEvent
	(*UserPresence)(nil),                     // 51: nakama.realtime.UserPresence
	nil,                                      // 52: nakama.realtime.Error.ContextEntry
	nil,                                      // 53: nakama.realtime.MatchJoin.MetadataEntry
	nil,                                      // 54: nakama.realtime.MatchmakerAdd.StringPropertiesEntry
	nil,                                      // 55: nakama.realtime.MatchmakerAdd.NumericPropertiesEntry
	(*MatchmakerMatched_MatchmakerUser)(nil), // 56: nakama.realtime.MatchmakerMatched.MatchmakerUser
	nil,                                      // 57: nakama.realtime.MatchmakerMatched.MatchmakerUser.StringPropertiesEntry
	nil,                                      // 58: nakama.realtime.MatchmakerMatched.MatchmakerUser.NumericPropertiesEntry
	nil,                                      // 59: nakama.realtime.PartyMatchmakerAdd.StringPropertiesEntry
	nil,                                      // 60: nakama.realtime.PartyMatchmakerAdd.NumericPropertiesEntry
	(*api.ChannelMessage)(nil),               // 61: nakama.api.ChannelMessage
	(*api.Rpc)(nil),                          // 62: nakama.api.Rpc
	(*wrapperspb.BoolValue)(nil),             // 63: google.protobuf.BoolValue
	(*wrapperspb.Int32Value)(nil),            // 64: google.protobuf.Int32Value
	(*timestamppb.Timestamp)(nil),            // 65: google.protobuf.Timestamp
	(*wrapperspb.StringValue)(nil),           // 66: google.protobuf.StringValue
	(*api.Notification)(nil),                 // 67: nakama.api.Notification
}
var file_realtime_proto_depIdxs = []int32{
	3,   // 0: nakama.realtime.Envelope.channel:type_name -> nakama.realtime.Channel
	4,   // 1: nakama.realtime.Envelope.channel_join:type_name -> nakama.realtime.ChannelJoin
	5,   // 2: nakama.realtime.Envelope.channel_leave:type_name -> nakama.realtime.ChannelLeave
	61,  // 3: nakama.realtime.Envelope.channel_message:type_name -> nakama.api.ChannelMessage
	6,   // 4: nakama.realtime.Envelope.channel_message_ack:type_name -> nakama.realtime.ChannelMessageAck
	7,   // 5: nakama.realtime.Envelope.channel_message_send:type_name -> nakama.realtime.ChannelMessageSend
	8,   // 6: nakama.realtime.Envelope.channel_message_update:type_name -> nakama.realtime.ChannelMessageUpdate
	9,   // 7: nakama.realtime.Envelope.channel_message_remove:type_name -> nakama.realtime.ChannelMessageRemove
	10,  // 8: nakama.realtime.Envelope.channel_presence_event:type_name -> nakama.realtime.ChannelPresenceEvent
	11,  // 9: nakama.realtime.Envelope.error:type_name -> nakama.realtime.Error
	12,  // 10: nakama.realtime.Envelope.match:type_name -> nakama.realtime.Match
	13,  // 11: nakama.realtime.Envelope.match_create:type_name -> nakama.realtime.MatchCreate
	14,  // 12: nakama.realtime.Envelope.match_data:type_name -> nakama.realtime.MatchData
	15,  // 13: nakama.realtime.Envelope.match_data_send:type_name -> nakama.realtime.MatchDataSend
	16,  // 14: nakama.realtime.Envelope.match_join:type_name -> nakama.realtime.MatchJoin
	17,  // 15: nakama.realtime.Envelope.match_leave:type_name -> nakama.realtime.MatchLeave
	18,  // 16: nakama.realtime.Envelope.match_presence_event:type_name -> nakama.realtime.MatchPresenceEvent
	19,  // 17: nakama.realtime.Envelope.matchmaker_add:type_name -> nakama.realtime.MatchmakerAdd
	20,  // 18: nakama.realtime.Envelope.matchmaker_matched:type_name -> nakama.realtime.MatchmakerMatched
	21,  // 19: nakama.realtime.Envelope.matchmaker_remove:type_name -> nakama.realtime.MatchmakerRemove
	22,  // 20: nakama.realtime.Envelope.matchmaker_ticket:type_name -> nakama.realtime.MatchmakerTicket
	23,  // 21: nakama.realtime.Envelope.notifications:type_name -> nakama.realtime.Notifications
	62,  // 22: nakama.realtime.Envelope.rpc:type_name -> nakama.api.Rpc
	43,  // 23: nakama.realtime.Envelope.status:type_name -> nakama.realtime.Status
	44,  // 24: nakama.realtime.Envelope.status_follow:type_name -> nakama.realtime.StatusFollow
	45,  // 25: nakama.realtime.Envelope.status_presence_event:type_name -> nakama.realtime.StatusPresenceEvent
	46,  // 26: nakama.realtime.Envelope.status_unfollow:type_name -> nakama.realtime.StatusUnfollow
	47,  // 27: nakama.realtime.Envelope.status_update:type_name -> nakama.realtime.StatusUpdate
	49,  // 28: nakama.realtime.Envelope.stream_data:type_name -> nakama.realtime.StreamData
	50,  // 29: nakama.realtime.Envelope.stream_presence_event:type_name -> nakama.realtime.StreamPresenceEvent
	41,  // 30: nakama.realtime.Envelope.ping:type_name -> nakama.realtime.Ping
	42,  // 31: nakama.realtime.Envelope.pong:type_name -> nakama.realtime.Pong
	24,  // 32: nakama.realtime.Envelope.party:type_name -> nakama.realtime.Party
	25,  // 33: nakama.realtime.Envelope.party_create:type_name -> nakama.realtime.PartyCreate
	26,  // 34: nakama.realtime.Envelope.party_join:type_name -> nakama.realtime.PartyJoin
	27,  // 35: nakama.realtime.Envelope.party_leave:type_name -> nakama.realtime.PartyLeave
	28,  // 36: nakama.realtime.Envelope.party_promote:type_name -> nakama.realtime.PartyPromote
	29,  // 37: nakama.realtime.Envelope.party_leader:type_name -> nakama.realtime.PartyLeader
	30,  // 38: nakama.realtime.Envelope.party_accept:type_name -> nakama.realtime.PartyAccept
	31,  // 39: nakama.realtime.Envelope.party_remove:type_name -> nakama.realtime.PartyRemove
	32,  // 40: nakama.realtime.Envelope.party_close:type_name -> nakama.realtime.PartyClose
	33,  // 41: nakama.realtime.Envelope.party_join_request_list:type_name -> nakama.realtime.PartyJoinRequestList
	34,  // 42: nakama.realtime.Envelope.party_join_request:type_name -> nakama.realtime.PartyJoinRequest
	35,  // 43: nakama.realtime.Envelope.party_matchmaker_add:type_name -> nakama.realtime.PartyMatchmakerAdd
	36,  // 44: nakama.realtime.Envelope.party_matchmaker_remove:type_name -> nakama.realtime.PartyMatchmakerRemove
	37,  // 45: nakama.realtime.Envelope.party_matchmaker_ticket:type_name -> nakama.realtime.PartyMatchmakerTicket
	38,  // 46: nakama.realtime.Envelope.party_data:type_name -> nakama.realtime.PartyData
	39,  // 47: nakama.realtime.Envelope.party_data_send:type_name -> nakama.realtime.PartyDataSend
	40,  // 48: nakama.realtime.Envelope.party_presence_event:type_name -> nakama.realtime.PartyPresenceEvent
	51,  // 49: nakama.realtime.Channel.presences:type_name -> nakama.realtime.UserPresence
	51,  // 50: nakama.realtime.Channel.self:type_name -> nakama.realtime.UserPresence
	63,  // 51: nakama.realtime.ChannelJoin.persistence:type_name -> google.protobuf.BoolValue
	63,  // 52: nakama.realtime.ChannelJoin.hidden:type_name -> google.protobuf.BoolValue
	64,  // 53: nakama.realtime.ChannelMessageAck.code:type_name -> google.protobuf.Int32Value
	65,  // 54: nakama.realtime.ChannelMessageAck.create_time:type_name -> google.protobuf.Timestamp
	65,  // 55: nakama.realtime.ChannelMessageAck.update_time:type_name -> google.protobuf.Timestamp
	63,  // 56: nakama.realtime.ChannelMessageAck.persistent:type_name -> google.protobuf.BoolValue
	51,  // 57: nakama.realtime.ChannelPresenceEvent.joins:type_name -> nakama.realtime.UserPresence
	51,  // 58: nakama.realtime.ChannelPresenceEvent.leaves:type_name -> nakama.realtime.UserPresence
	52,  // 59: nakama.realtime.Error.context:type_name -> nakama.realtime.Error.ContextEntry
	66,  // 60: nakama.realtime.Match.label:type_name -> google.protobuf.StringValue
	51,  // 61: nakama.realtime.Match.presences:type_name -> nakama.realtime.UserPresence
	51,  // 62: nakama.realtime.Match.self:type_name -> nakama.realtime.UserPresence
	51,  // 63: nakama.realtime.MatchData.presence:type_name -> nakama.realtime.UserPresence
	51,  // 64: nakama.realtime.MatchDataSend.presences:type_name -> nakama.realtime.UserPresence
	53,  // 65: nakama.realtime.MatchJoin.metadata:type_name -> nakama.realtime.MatchJoin.MetadataEntry
	51,  // 66: nakama.realtime.MatchPresenceEvent.joins:type_name -> nakama.realtime.UserPresence
	51,  // 67: nakama.realtime.MatchPresenceEvent.leaves:type_name -> nakama.realtime.UserPresence
	54,  // 68: nakama.realtime.MatchmakerAdd.string_properties:type_name -> nakama.realtime.MatchmakerAdd.StringPropertiesEntry
	55,  // 69: nakama.realtime.MatchmakerAdd.numeric_properties:type_name -> nakama.realtime.MatchmakerAdd.NumericPropertiesEntry
	64,  // 70: nakama.realtime.MatchmakerAdd.count_multiple:type_name -> google.protobuf.Int32Value
	56,  // 71: nakama.realtime.MatchmakerMatched.users:type_name -> nakama.realtime.MatchmakerMatched.MatchmakerUser
	56,  // 72: nakama.realtime.MatchmakerMatched.self:type_name -> nakama.realtime.MatchmakerMatched.MatchmakerUser
	67,  // 73: nakama.realtime.Notifications.notifications:type_name -> nakama.api.Notification
	51,  // 74: nakama.realtime.Party.self:type_name -> nakama.realtime.UserPresence
	51,  // 75: nakama.realtime.Party.leader:type_name -> nakama.realtime.UserPresence
	51,  // 76: nakama.realtime.Party.presences:type_name -> nakama.realtime.UserPresence
	51,  // 77: nakama.realtime.PartyPromote.presence:type_name -> nakama.realtime.UserPresence
	51,  // 78: nakama.realtime.PartyLeader.presence:type_name -> nakama.realtime.UserPresence
	51,  // 79: nakama.realtime.PartyAccept.presence:type_name -> nakama.realtime.UserPresence
	51,  // 80: nakama.realtime.PartyRemove.presence:type_name -> nakama.realtime.UserPresence
	51,  // 81: nakama.realtime.PartyJoinRequest.presences:type_name -> nakama.realtime.UserPresence
	59,  // 82: nakama.realtime.PartyMatchmakerAdd.string_properties:type_name -> nakama.realtime.PartyMatchmakerAdd.StringPropertiesEntry
	60,  // 83: nakama.realtime.PartyMatchmakerAdd.numeric_properties:type_name -> nakama.realtime.PartyMatchmakerAdd.NumericPropertiesEntry
	64,  // 84: nakama.realtime.PartyMatchmakerAdd.count_multiple:type_name -> google.protobuf.Int32Value
	51,  // 85: nakama.realtime.PartyData.presence:type_name -> nakama.realtime.UserPresence
	51,  // 86: nakama.realtime.PartyPresenceEvent.joins:type_name -> nakama.realtime.UserPresence
	51,  // 87: nakama.realtime.PartyPresenceEvent.leaves:type_name -> nakama.realtime.UserPresence
	51,  // 88: nakama.realtime.Status.presences:type_name -> nakama.realtime.UserPresence
	51,  // 89: nakama.realtime.StatusPresenceEvent.joins:type_name -> nakama.realtime.UserPresence
	51,  // 90: nakama.realtime.StatusPresenceEvent.leaves:type_name -> nakama.realtime.UserPresence
	66,  // 91: nakama.realtime.StatusUpdate.status:type_name -> google.protobuf.StringValue
	48,  // 92: nakama.realtime.StreamData.stream:type_name -> nakama.realtime.Stream
	51,  // 93: nakama.realtime.StreamData.sender:type_name -> nakama.realtime.UserPresence
	48,  // 94: nakama.realtime.StreamPresenceEvent.stream:type_name -> nakama.realtime.Stream
	51,  // 95: nakama.realtime.StreamPresenceEvent.joins:type_name -> nakama.realtime.UserPresence
	51,  // 96: nakama.realtime.StreamPresenceEvent.leaves:type_name -> nakama.realtime.UserPresence
	66,  // 97: nakama.realtime.UserPresence.status:type_name -> google.protobuf.StringValue
	51,  // 98: nakama.realtime.MatchmakerMatched.MatchmakerUser.presence:type_name -> nakama.realtime.UserPresence
	57,  // 99: nakama.realtime.MatchmakerMatched.MatchmakerUser.string_properties:type_name -> nakama.realtime.MatchmakerMatched.MatchmakerUser.StringPropertiesEntry
	58,  // 100: nakama.realtime.MatchmakerMatched.MatchmakerUser.numeric_properties:type_name -> nakama.realtime.MatchmakerMatched.MatchmakerUser.NumericPropertiesEntry
	101, // [101:101] is the sub-list for method output_type
	101, // [101:101] is the sub-list for method input_type
	101, // [101:101] is the sub-list for extension type_name
	101, // [101:101] is the sub-list for extension extendee
	0,   // [0:101] is the sub-list for field type_name
}

func init() { file_realtime_proto_init() }
func file_realtime_proto_init() {
	if File_realtime_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_realtime_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Envelope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Channel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelLeave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelMessageAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelMessageSend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelMessageUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelMessageRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelPresenceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Match); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchCreate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchDataSend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchLeave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchPresenceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchmakerAdd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchmakerMatched); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchmakerRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchmakerTicket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Notifications); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Party); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyCreate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyLeave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyPromote); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyLeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyAccept); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyClose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyJoinRequestList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyJoinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyMatchmakerAdd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyMatchmakerRemove); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyMatchmakerTicket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyDataSend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PartyPresenceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pong); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusFollow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusPresenceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusUnfollow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stream); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamPresenceEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPresence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_realtime_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchmakerMatched_MatchmakerUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_realtime_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Envelope_Channel)(nil),
		(*Envelope_ChannelJoin)(nil),
		(*Envelope_ChannelLeave)(nil),
		(*Envelope_ChannelMessage)(nil),
		(*Envelope_ChannelMessageAck)(nil),
		(*Envelope_ChannelMessageSend)(nil),
		(*Envelope_ChannelMessageUpdate)(nil),
		(*Envelope_ChannelMessageRemove)(nil),
		(*Envelope_ChannelPresenceEvent)(nil),
		(*Envelope_Error)(nil),
		(*Envelope_Match)(nil),
		(*Envelope_MatchCreate)(nil),
		(*Envelope_MatchData)(nil),
		(*Envelope_MatchDataSend)(nil),
		(*Envelope_MatchJoin)(nil),
		(*Envelope_MatchLeave)(nil),
		(*Envelope_MatchPresenceEvent)(nil),
		(*Envelope_MatchmakerAdd)(nil),
		(*Envelope_MatchmakerMatched)(nil),
		(*Envelope_MatchmakerRemove)(nil),
		(*Envelope_MatchmakerTicket)(nil),
		(*Envelope_Notifications)(nil),
		(*Envelope_Rpc)(nil),
		(*Envelope_Status)(nil),
		(*Envelope_StatusFollow)(nil),
		(*Envelope_StatusPresenceEvent)(nil),
		(*Envelope_StatusUnfollow)(nil),
		(*Envelope_StatusUpdate)(nil),
		(*Envelope_StreamData)(nil),
		(*Envelope_StreamPresenceEvent)(nil),
		(*Envelope_Ping)(nil),
		(*Envelope_Pong)(nil),
		(*Envelope_Party)(nil),
		(*Envelope_PartyCreate)(nil),
		(*Envelope_PartyJoin)(nil),
		(*Envelope_PartyLeave)(nil),
		(*Envelope_PartyPromote)(nil),
		(*Envelope_PartyLeader)(nil),
		(*Envelope_PartyAccept)(nil),
		(*Envelope_PartyRemove)(nil),
		(*Envelope_PartyClose)(nil),
		(*Envelope_PartyJoinRequestList)(nil),
		(*Envelope_PartyJoinRequest)(nil),
		(*Envelope_PartyMatchmakerAdd)(nil),
		(*Envelope_PartyMatchmakerRemove)(nil),
		(*Envelope_PartyMatchmakerTicket)(nil),
		(*Envelope_PartyData)(nil),
		(*Envelope_PartyDataSend)(nil),
		(*Envelope_PartyPresenceEvent)(nil),
	}
	file_realtime_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*MatchJoin_MatchId)(nil),
		(*MatchJoin_Token)(nil),
	}
	file_realtime_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*MatchmakerMatched_MatchId)(nil),
		(*MatchmakerMatched_Token)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_realtime_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_realtime_proto_goTypes,
		DependencyIndexes: file_realtime_proto_depIdxs,
		EnumInfos:         file_realtime_proto_enumTypes,
		MessageInfos:      file_realtime_proto_msgTypes,
	}.Build()
	File_realtime_proto = out.File
	file_realtime_proto_rawDesc = nil
	file_realtime_proto_goTypes = nil
	file_realtime_proto_depIdxs = nil
}
