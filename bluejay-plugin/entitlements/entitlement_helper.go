package entitlements

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/api"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/storage"
)

type Item struct {
	ItemId       string
	PriceOptions []map[string]uint16
	IsActive     bool
	Tags         []string
	Grants       *map[string]uint16
	Metadata     map[string]interface{}
}

type UserEntitlementResponse struct {
	IDs []string
}

type UserIDRequest struct {
	ID string
}

type SectionIDsRequest UserEntitlementResponse
type BundleIDsRequest UserEntitlementResponse
type ItemIDsRequest UserEntitlementResponse
type TreasureTroveIDsRequest UserEntitlementResponse
type TrovePageIDsRequest UserEntitlementResponse
type GrantEntitlementsRequest UserEntitlementResponse
type EntitlementsUnlocked UserEntitlementResponse

type GrantCurrencyPayload struct {
	UserId   string
	Delta    int64
	Currency string
}

type UserEntitlements struct {
	Entitlements map[string]any
}

type FeaturedCharacter struct {
	CharacterIndexID string
}

type FeaturedWeapon struct {
	WeaponIndexID string
}

type FeaturedMount struct {
	MountIndexID string
}

type EquippedLoadout struct {
	FeaturedCharacterIndexId string
	FeaturedWeaponIndexId    string
	FeaturedMountIndexId     string
	Characters               map[string]UserEntitlementResponse
	Weapons                  map[string]UserEntitlementResponse
	Mounts                   map[string]UserEntitlementResponse
	RaidTools                map[string]UserEntitlementResponse
	PlayerItems              []string
}

type EquipPayloadForComponent struct {
	ID                    string
	LoadoutEntitlementIDs []string
}

// GetUserInfoPayload Generic request payload to load user info
type GetUserInfoPayload struct {
	UserID string
	Cursor string
}

// PurchaseResult Results of Purchase operation
type PurchaseResult struct {
	StorageUpdates []*api.StorageObjectAck
	WalletUpdates  []*runtime.WalletUpdateResult
}

func writeUserLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, version string, loadout EquippedLoadout) (string, error) {

	if loadoutJSON, err := json.Marshal(loadout); err != nil {
		return "", err
	} else {

		writeRequest := &runtime.StorageWrite{
			Collection:      storage.LOADOUT_COLLECTION,
			Key:             storage.EQUIPPED_LOADOUT,
			UserID:          userId,
			Version:         version,
			Value:           string(loadoutJSON),
			PermissionRead:  1,
			PermissionWrite: 1,
		}

		if _, err := nk.StorageWrite(ctx, []*runtime.StorageWrite{writeRequest}); err != nil {
			return "", err
		} else {
			return "", nil
		}
	}
}

func ReadUserLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (EquippedLoadout, string, error) {

	readRequest := &runtime.StorageRead{
		Collection: storage.LOADOUT_COLLECTION,
		Key:        storage.EQUIPPED_LOADOUT,
		UserID:     userId,
	}

	if readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return EquippedLoadout{}, "", runtime.NewError("Error read DB error.", 2)
	} else if len(readResult) == 0 {
		// If the user has zero character loadout info Set, initialize the map
		// Then Set all the character keys that are missing (if new, all would be missing, and we Set all, or we Set only the missing ones)
		equippedLoadout := populateDefaultInfoIfMissing(EquippedLoadout{}, logger)

		return equippedLoadout, "", nil
	} else {
		var equippedLoadout EquippedLoadout
		if err := json.Unmarshal([]byte(readResult[0].Value), &equippedLoadout); err != nil {
			logger.Error(fmt.Sprintf("Could not unmarshall string into json %s", readResult[0].Value))
			return EquippedLoadout{}, "", err
		}

		equippedLoadout = populateDefaultInfoIfMissing(equippedLoadout, logger)

		return equippedLoadout, readResult[0].Version, nil
	}
}

func ReadEntitlementsWithDefaultsPopulated(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (string, error) {
	if userEntitlements, err := ReadEntitlements(ctx, nk, userId); err != nil {
		logger.Error("Error fetching entitlement details", err)
		return "", err
	} else {
		defaultEntitlements := populateDefaultEntitlementsAlways()
		userEntitlements.IDs = append(userEntitlements.IDs, defaultEntitlements...)

		if entitlementJson, err := json.Marshal(userEntitlements); err != nil {
			logger.Error("Error formatting JSON string", err)
			return "", err
		} else {

			logger.Debug("Response %s", string(entitlementJson))
			return string(entitlementJson), nil
		}
	}
}

func ReadEntitlements(ctx context.Context, nk runtime.NakamaModule, userID string) (UserEntitlementResponse, error) {

	var uer = UserEntitlementResponse{}

	// Read from new location
	ue, _, err := ReadEntitlementsAsMap(ctx, nk, userID)

	if err != nil {
		return UserEntitlementResponse{}, err
	}

	// If we did find entitlements, convert to list format for compatability
	for key := range ue.Entitlements {
		uer.IDs = append(uer.IDs, key)
	}

	return uer, err

}

func ReadEntitlementsAsMap(ctx context.Context, nk runtime.NakamaModule, userID string) (UserEntitlements, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.ENTITLEMENT_COLLECTION,
		Key:        storage.ENTITLEMENTS,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	if err != nil {
		return UserEntitlements{}, "", err
	}

	if len(readResult) == 0 {
		return UserEntitlements{}, "", nil
	}

	if len(readResult) != 1 {
		return UserEntitlements{}, "", runtime.NewError(fmt.Sprintf("More than one entitlement result found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	userEntitlement := UserEntitlements{}

	err = json.Unmarshal([]byte(result.Value), &userEntitlement)

	if err != nil {
		return UserEntitlements{}, "", err
	}

	return userEntitlement, result.Version, err
}

// true if user has all entitlements, otherwise false
func HasEntitlements(ctx context.Context, nk runtime.NakamaModule, userID string, entitlements []string, logger runtime.Logger) (bool, error) {

	if len(entitlements) == 0 {
		return true, nil
	}

	logger.Debug(fmt.Sprintf("Checking to see if user has entitlements already: %s", entitlements))

	if userEntitlements, _, err := ReadEntitlementsAsMap(ctx, nk, userID); err != nil {
		return false, err
	} else {

		logger.Debug(fmt.Sprintf("Users existing entitlements: %s", userEntitlements))

		for _, entitlement := range entitlements {
			if _, ok := userEntitlements.Entitlements[entitlement]; !ok {
				return false, nil
			}
		}

		return true, nil
	}
}

func ResetUserLoadoutHelper(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (EquippedLoadout, error) {

	readRequest := &runtime.StorageDelete{
		Collection: storage.LOADOUT_COLLECTION,
		Key:        storage.EQUIPPED_LOADOUT,
		UserID:     userId,
	}

	if err := nk.StorageDelete(ctx, []*runtime.StorageDelete{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return EquippedLoadout{}, runtime.NewError("Error read DB error.", 2)
	} else {
		equippedLoadout := populateDefaultInfoIfMissing(EquippedLoadout{}, logger)

		return equippedLoadout, nil
	}
}

func ResetAllUsersLoadoutHelper(ctx context.Context, nk runtime.NakamaModule, logger runtime.Logger) error {

	var loadoutCursor string

	userIds := []string{}

	if results, cursor, err := nk.StorageList(ctx, "", "", storage.LOADOUT_COLLECTION, 500, ""); err != nil {
		return err
	} else {
		loadoutCursor = cursor

		loadoutDeletes := []*runtime.StorageDelete{}

		for _, userLoadout := range results {
			logger.Warn("Deleting loadout for user : %s", userLoadout.UserId)
			userIds = append(userIds, userLoadout.UserId)

			deleteRequest := runtime.StorageDelete{
				Collection: storage.LOADOUT_COLLECTION,
				Key:        storage.EQUIPPED_LOADOUT,
				UserID:     userLoadout.UserId,
			}

			loadoutDeletes = append(loadoutDeletes, &deleteRequest)
		}

		for loadoutCursor != "" {
			if results, cursor, err := nk.StorageList(ctx, "", "", storage.LOADOUT_COLLECTION, 500, ""); err != nil {
				return err
			} else {
				loadoutCursor = cursor

				for _, userLoadout := range results {
					deleteRequest := runtime.StorageDelete{
						Collection: storage.LOADOUT_COLLECTION,
						Key:        storage.EQUIPPED_LOADOUT,
						UserID:     userLoadout.UserId,
					}

					loadoutDeletes = append(loadoutDeletes, &deleteRequest)
				}
			}
		}

		if err := nk.StorageDelete(ctx, loadoutDeletes); err != nil {
			return err
		} else {
			logger.Info("Deleted loadout for users : %v", userIds)
			return nil
		}

	}
}

func populateDefaultEntitlementsAlways() []string {
	defaultCharacterSkins := []string{
		"MTX.CharacterSkins.Redmane.DefaultSkin",
		"MTX.CharacterSkins.Mara.DefaultSkin",
		"MTX.CharacterSkins.Condor.DefaultSkin",
		"MTX.CharacterSkins.Atticus.DefaultSkin",
		"MTX.CharacterSkins.Jade.DefaultSkin",
		"MTX.CharacterSkins.Alchemist.DefaultSkin",
		"MTX.CharacterSkins.Una.DefaultSkin",
		"MTX.CharacterSkins.Hunter.DefaultSkin",
		"MTX.CharacterSkins.Rogue.DefaultSkin",
		"MTX.CharacterSkins.Bronco.DefaultSkin",
		"MTX.CharacterSkins.Sawtooth.DefaultSkin",
	}

	defaultWeaponSkins := []string{
		"MTX.WeaponSkins.BigRig.Default",
		"MTX.WeaponSkins.BurstRifle.Default",
		"MTX.WeaponSkins.Corsair.Default",
		"MTX.WeaponSkins.DB51.Default",
		"MTX.WeaponSkins.Dynasty.Default",
		"MTX.WeaponSkins.Longhorn.Default",
		"MTX.WeaponSkins.Nova.Default",
		"MTX.WeaponSkins.Paladin.Default",
		"MTX.WeaponSkins.Scout.Default",
		"MTX.WeaponSkins.Viper.Default",
	}

	playerItems := []string{
		"MTX.AccountBanners.Default",
	}

	return append(append(defaultCharacterSkins, defaultWeaponSkins...), playerItems...)
}

func populateDefaultInfoIfMissing(equippedLoadout EquippedLoadout, _ runtime.Logger) EquippedLoadout {

	if len(equippedLoadout.FeaturedCharacterIndexId) == 0 || equippedLoadout.FeaturedCharacterIndexId == "" {
		equippedLoadout.FeaturedCharacterIndexId = "Classes.Class.Redmane"
	}

	// Note this will be packaged in / pulled in from Satori or elsewhere down the line
	defaultCharacterSkins := map[string]string{
		"Classes.Class.Redmane":   "MTX.CharacterSkins.Redmane.DefaultSkin",
		"Classes.Class.Mara":      "MTX.CharacterSkins.Mara.DefaultSkin",
		"Classes.Class.Condor":    "MTX.CharacterSkins.Condor.DefaultSkin",
		"Classes.Class.Atticus":   "MTX.CharacterSkins.Atticus.DefaultSkin",
		"Classes.Class.Jade":      "MTX.CharacterSkins.Jade.DefaultSkin",
		"Classes.Class.Alchemist": "MTX.CharacterSkins.Alchemist.DefaultSkin",
		"Classes.Class.Buddy":     "MTX.CharacterSkins.Una.DefaultSkin",
		"Classes.Class.Hunter":    "MTX.CharacterSkins.Hunter.DefaultSkin",
		"Classes.Class.Rogue":     "MTX.CharacterSkins.Rogue.DefaultSkin",
		"Classes.Class.Bronco":    "MTX.CharacterSkins.Bronco.DefaultSkin",
		"Classes.Class.Sawtooth":  "MTX.CharacterSkins.Sawtooth.DefaultSkin",
	}

	defaultWeaponSkins := map[string]string{
		"Weapons.Assault.DB51":         "MTX.WeaponSkins.DB51.Default",
		"Weapons.Assault.DSBurst":      "MTX.WeaponSkins.BurstRifle.Default",
		"Weapons.Assault.Dynasty":      "MTX.WeaponSkins.Dynasty.Default",
		"Weapons.Handgun.Longhorn":     "MTX.WeaponSkins.Longhorn.Default",
		"Weapons.Launcher.RS5":         "MTX.WeaponSkins.RocketLauncher.Default",
		"Weapons.LMG.BigRig":           "MTX.WeaponSkins.BigRig.Default",
		"Weapons.Melee.Axe":            "MTX.WeaponSkins.Axe.DefaultSkin",
		"Weapons.RaidTool.BlastHammer": "MTX.WeaponSkins.RaidHammer.Default",
		"Weapons.RaidTool.ZiplineGun":  "MTX.WeaponSkins.ZiplineGun.Default",
		"Weapons.Shotgun.Nova":         "MTX.WeaponSkins.Nova.Default",
		"Weapons.Shotgun.Paladin":      "MTX.WeaponSkins.Paladin.Default",
		"Weapons.SMG.Corsair":          "MTX.WeaponSkins.Corsair.Default",
		"Weapons.SMG.Viper":            "MTX.WeaponSkins.Viper.Default",
		"Weapons.Sniper.Scout":         "MTX.WeaponSkins.Scout.Default",
	}

	defaultMountSkins := map[string]string{
		"Mounts.Horse": "MTX.MountSkins.Horse.DefaultSkin",
		"Mounts.Cat":   "MTX.MountSkins.Cat.DefaultSkin",
		"Mounts.Bear":  "MTX.MountSkins.Bear.DefaultSkin",
	}

	defaultPlayerItems := []string{
		"MTX.AccountBanners.Default",
	}

	if equippedLoadout.PlayerItems == nil {
		equippedLoadout.PlayerItems = defaultPlayerItems
	}

	if equippedLoadout.Characters == nil {
		// logger.Info("Creating new Characters map for loadout")
		equippedLoadout.Characters = map[string]UserEntitlementResponse{}
	}

	for character, defaultSkin := range defaultCharacterSkins {
		// logger.Info("Checking if user has key for character %s", character)

		if charLoadout, ok := equippedLoadout.Characters[character]; !ok {
			defaultSkinLoadout := UserEntitlementResponse{IDs: []string{defaultSkin}}
			// logger.Info("User does not have key for %s. Adding default loadout %v", character, defaultSkinLoadout)
			equippedLoadout.Characters[character] = defaultSkinLoadout
		} else if len(charLoadout.IDs) == 0 {
			skinsArray := []string{defaultSkin}
			// logger.Info("User does have key for %s but no entitlements, adding array %v", character, skinsArray)
			charLoadout.IDs = skinsArray
			equippedLoadout.Characters[character] = charLoadout
		}
	}

	if equippedLoadout.Weapons == nil {
		// logger.Info("Creating new Weapons map for loadout")
		equippedLoadout.Weapons = map[string]UserEntitlementResponse{}
	}

	for weaponId, weaponSkin := range defaultWeaponSkins {
		// logger.Info("Checking if user has key for weaponId %s", weaponId)
		if weaponLoadout, ok := equippedLoadout.Weapons[weaponId]; !ok {
			defaultWeaponLoadout := UserEntitlementResponse{IDs: []string{weaponSkin}}
			// logger.Info("User does not have key for %s. Adding default loadout %v", weaponId, defaultWeaponLoadout)
			equippedLoadout.Weapons[weaponId] = defaultWeaponLoadout
		} else if len(weaponLoadout.IDs) == 0 {
			skinsArray := []string{weaponSkin}
			// logger.Info("User does have key for %s but no entitlements, adding array %v", weaponId, skinsArray)
			weaponLoadout.IDs = skinsArray
			equippedLoadout.Weapons[weaponId] = weaponLoadout
		}
	}

	if equippedLoadout.Mounts == nil {
		equippedLoadout.Mounts = map[string]UserEntitlementResponse{}
	}

	for mountId, mountSkin := range defaultMountSkins {
		if mountLoadout, ok := equippedLoadout.Mounts[mountId]; !ok {
			defaultMountLoadout := UserEntitlementResponse{IDs: []string{mountSkin}}
			equippedLoadout.Mounts[mountId] = defaultMountLoadout
		} else if len(mountLoadout.IDs) == 0 {
			skinsArray := []string{mountSkin}
			mountLoadout.IDs = skinsArray
			equippedLoadout.Mounts[mountId] = mountLoadout
		}
	}

	return equippedLoadout
}

func grantUserEntitlementDebugAPI(ctx context.Context, nk runtime.NakamaModule, entitlements []string, userID string) (*runtime.StorageWrite, error) {

	userEntitlements, version, err := ReadEntitlementsAsMap(ctx, nk, userID)

	if err != nil {
		return nil, err
	}

	for _, entitlement := range entitlements {
		userEntitlements.Entitlements[entitlement] = struct{}{}
	}

	newEntitlements, err := json.Marshal(userEntitlements)

	if err != nil {
		return nil, err
	}

	storageWrite := &runtime.StorageWrite{
		Collection:      storage.ENTITLEMENT_COLLECTION,
		Key:             storage.ENTITLEMENTS,
		UserID:          userID,
		PermissionRead:  1,
		PermissionWrite: 1,
		Value:           string(newEntitlements),
		Version:         version,
	}

	return storageWrite, nil
}
