package matchmaking

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/playlists"
	"wildlight.gg/bluejay/storage"
)

type matchmakerPlayer struct {
	Id     string
	Rating types.Rating
	Entry  runtime.MatchmakerEntry
}

type skillRecord struct {
	Mu    float64
	Sigma float64
}

type playerSkillRecord struct {
	Buckets map[string]skillRecord
}

func newPlayerSkillRecord() playerSkillRecord {
	return playerSkillRecord{
		Buckets: map[string]skillRecord{},
	}
}

func readSkill(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, playerIds []string) (map[string]playerSkillRecord, error) {
	readRequest := make([]*runtime.StorageRead, len(playerIds))

	result := map[string]playerSkillRecord{}
	for i, playerId := range playerIds {
		readRequest[i] = &runtime.StorageRead{
			Collection: storage.MATCHMAKING_COLLECTION,
			Key:        storage.USER_SKILL + "." + environment,
			UserID:     playerId,
		}
	}

	skillRead, err := nk.StorageRead(ctx, readRequest)
	if err != nil {
		logger.WithField("err", err).Error("error reading skill from storage")
		return nil, err
	} else {
		for _, record := range skillRead {
			userId := record.GetUserId()

			var skills playerSkillRecord
			err = json.Unmarshal([]byte(record.Value), &skills)
			if err != nil {
				logger.WithField("err", err).WithField("userId", userId).WithField("json", record.Value).Error("error unmarshalling skill")
			} else {
				result[userId] = skills
			}
		}
	}

	for _, playerId := range playerIds {
		_, found := result[playerId]
		if !found {
			result[playerId] = newPlayerSkillRecord()
		}
	}

	return result, nil
}

func getDefaultSkill(skillBucket *playlists.SkillBucket) skillRecord {
	return skillRecord{
		Mu:    skillBucket.DefaultMu,
		Sigma: skillBucket.DefaultSigma,
	}
}

func getSkillForBucket(skillBucket *playlists.SkillBucket, playerSkillRecord playerSkillRecord) skillRecord {
	bucket, found := playerSkillRecord.Buckets[skillBucket.Name]
	if found {
		return bucket
	}
	return getDefaultSkill(skillBucket)
}

func updateSkill(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, players map[string]playerSkillRecord) error {
	updateRequest := make([]*runtime.StorageWrite, len(players))

	i := 0
	for playerId, skill := range players {
		jsonSkill, err := json.Marshal(skill)
		if err != nil {
			return err
		}

		updateRequest[i] = &runtime.StorageWrite{
			Collection: storage.MATCHMAKING_COLLECTION,
			Key:        storage.USER_SKILL + "." + environment,
			UserID:     playerId,
			Value:      string(jsonSkill),
		}
		i++
	}

	result, err := nk.StorageWrite(ctx, updateRequest)
	if err != nil {
		return err
	}

	logger.WithField("ack", result).Info(fmt.Sprintf("Updated skill records for %d players", len(players)))

	return nil
}
