package matchmaking

import (
	"github.com/intinig/go-openskill/rating"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/playlists"
)

type matchmakerMatch struct {
	MatchId     string
	MapName     string
	ModeName    string
	SkillBucket string
	Teams       []*matchmakerTeam
}

func NewMatchmakerMatch(teamCount int) *matchmakerMatch {
	newMatch := &matchmakerMatch{
		Teams: make([]*matchmakerTeam, teamCount),
	}
	for i := 0; i < teamCount; i++ {
		newMatch.Teams[i] = &matchmakerTeam{}
	}

	return newMatch
}

// Get the predicted win % for each team in a proposed match
func (match matchmakerMatch) getPrediction(skillBucket *playlists.SkillBucket) []float64 {
	teams := make([]types.Team, len(match.Teams))
	for teamIndex, team := range match.Teams {
		teams[teamIndex] = types.Team{}
		for _, ticket := range team.Tickets {
			teams[teamIndex] = append(teams[teamIndex], ticket.Team...)
		}
	}

	return rating.PredictWin(teams, skillBucket.GetOptions())
}

// Calculate a score for this team composition based on the likelihood of each team winning. Lower is better.
func (match matchmakerMatch) Score(skillBucket *playlists.SkillBucket) float64 {
	odds := match.getPrediction(skillBucket)
	top := 0.0
	bottom := 1.0
	for _, chance := range odds {
		if chance > top {
			top = chance
		}
		if chance < bottom {
			bottom = chance
		}
	}

	return 100 * (top - bottom)
}
