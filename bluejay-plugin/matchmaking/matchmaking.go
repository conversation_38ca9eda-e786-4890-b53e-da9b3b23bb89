package matchmaking

import (
	"cmp"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand/v2"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/google/uuid"
	"github.com/heroiclabs/nakama-common/rtapi"
	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/intinig/go-openskill/rating"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/playlists"
	"wildlight.gg/bluejay/utils"
)

type platform int

const (
	platformNone platform = iota
	platformMixed
	platformXbox
	platformPlaystation
	platformPC
)

const EnvironmentProperty = "env"
const PlaylistProperty = "playlist"
const BuildProperty = "build"
const TeamNumberProperty = "teamNum"
const MatchIdProperty = "matchId"
const MapNameProperty = "map"
const ModeNameProperty = "mode"
const MuPropertySuffix = "_mu"
const SigmaPropertySuffix = "_sigma"
const PlatformPropertySuffix = "_platform"

type playlistKey struct {
	Environment string
	Playlist    string
	Build       string
}

type Matchmaker struct {
	PlaylistManager  *playlists.PlaylistManager
	PlayerMetaSystem *utils.PlayerMetaSystem
}

type playerRanking struct {
	OnlineId   string `json:"onlineId"`
	TeamNumber int    `json:"teamNumber"`
	Score      int    `json:"score"`
}

type submitPlayerRankingsRequest struct {
	PlayerRankings []playerRanking `json:"playerRankings"`
	Environment    string          `json:"environment"`
	PlaylistName   string          `json:"playlistName"`
}

func NewMatchmaker(ctx context.Context, logger runtime.Logger, initializer runtime.Initializer, playlistManager *playlists.PlaylistManager, playerMetaSystem *utils.PlayerMetaSystem) (*Matchmaker, error) {
	matchmaker := &Matchmaker{
		PlaylistManager:  playlistManager,
		PlayerMetaSystem: playerMetaSystem,
	}

	err := initializer.RegisterMatchmakerOverride(matchmaker.matchmakerOverride)
	if err != nil {
		logger.Error("error registering MatchmakerOverride: %v", err)
		return nil, err
	}

	err = initializer.RegisterBeforeRt("MatchmakerAdd", matchmaker.beforeMatchmakerAdd)
	if err != nil {
		logger.Error("Error registering BeforeMatchmakerAdd: %v", err)
		return nil, err
	}

	err = initializer.RegisterBeforeRt("PartyMatchmakerAdd", matchmaker.beforePartyMatchmakerAdd)
	if err != nil {
		logger.Error("Error registering BeforePartyMatchmakerAdd: %v", err)
		return nil, err
	}

	err = utils.RegisterWildlightRpc(initializer, logger, "submitPlayerRankings", matchmaker.submitPlayerRankingsRpc)
	if err != nil {
		logger.Error("Error registering submitPlayerRankings rpc: %v", err)
		return nil, err
	}

	err = utils.RegisterWildlightRpc(initializer, logger, "setPingResults", matchmaker.setPingResults)
	if err != nil {
		logger.Error("Error registering setPingResults rpc: %v", err)
		return nil, err
	}

	return matchmaker, nil
}

func getFloatProperty(propertyName string, properties map[string]interface{}, defaultValue float64) (float64, bool) {
	prop, found := properties[propertyName]
	if found {
		switch prop.(type) {
		case float64:
			return prop.(float64), true
		case string:
			val, err := strconv.ParseFloat(prop.(string), 64)
			if err != nil {
				return defaultValue, false
			}
			return val, true
		default:
		}
	}

	return defaultValue, false
}

type playlistGroup struct {
	tickets          []*matchmakerTicket
	playlist         *playlists.Playlist
	skillBucket      *playlists.SkillBucket
	matchmakerConfig *playlists.MatchmakerConfig
}

func (m *Matchmaker) matchmakerOverride(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, candidateMatches [][]runtime.MatchmakerEntry) [][]runtime.MatchmakerEntry {

	defer func() {
		err := recover()
		if err != nil {
			if utils.UseSentry {
				hub := sentry.GetHubFromContext(ctx)
				if hub == nil {
					hub = sentry.CurrentHub()
				}

				hub.RecoverWithContext(ctx, err)
			}

			logger.WithField("panic", err).Error("Recovering from panic in matchmaking")
		}
	}()

	playlistGroups := map[playlistKey]*playlistGroup{}

	for c := range candidateMatches {
		props := candidateMatches[c][0].GetProperties()
		key := playlistKey{
			Environment: props[EnvironmentProperty].(string),
			Playlist:    props[PlaylistProperty].(string),
			Build:       props[BuildProperty].(string),
		}

		playlistBucket, playlistFound := playlistGroups[key]
		if !playlistFound {
			playlist, skillBucket, matchmakerConfig, err := m.PlaylistManager.GetPlaylistConfigs(key.Playlist, key.Environment)
			if err != nil {
				continue
			}

			playlistBucket = &playlistGroup{
				playlist:         playlist,
				skillBucket:      skillBucket,
				matchmakerConfig: matchmakerConfig,
				tickets:          []*matchmakerTicket{},
			}

			playlistGroups[key] = playlistBucket
		}

		ticket := makeMatchmakerTicket(candidateMatches[c], playlistBucket.skillBucket, logger)

		playlistBucket.tickets = append(playlistBucket.tickets, ticket)
	}

	var matches []*matchmakerMatch
	var results [][]runtime.MatchmakerEntry

	for key, playlistBucket := range playlistGroups {
		teamSize := playlistBucket.playlist.TeamSize
		teamCount := playlistBucket.playlist.TeamCount
		matchSize := teamSize * teamCount

		startTime := time.Now()
		slices.SortFunc(playlistBucket.tickets, func(a, b *matchmakerTicket) int {
			return cmp.Compare(a.Skill(), b.Skill())
		})

		startOffset := rand.IntN(len(playlistBucket.tickets))
		playerCount := 0
		for i := 0; i < len(playlistBucket.tickets); i++ {
			playerCount += playlistBucket.tickets[i].PlayerCount()
		}

		for baseIndex := 0; baseIndex < len(playlistBucket.tickets); baseIndex++ {
			baseTicketIndex := (baseIndex + startOffset) % len(playlistBucket.tickets)
			baseTicket := playlistBucket.tickets[baseTicketIndex]
			if baseTicket == nil {
				continue
			}

			if matchSize == 1 {
				newMatch := NewMatchmakerMatch(teamCount)
				newMatch.MatchId = uuid.Must(uuid.NewRandom()).String()
				newMatch.MapName, newMatch.ModeName = playlistBucket.playlist.PickRandomMapMode()
				newMatch.SkillBucket = playlistBucket.skillBucket.Name

				newMatch.Teams[0].Tickets = append(newMatch.Teams[0].Tickets, baseTicket)
				matches = append(matches, newMatch)
				playlistBucket.tickets[baseTicketIndex] = nil
			} else {
				startRangeIndex := -1
				endRangeIndex := -1
				rangePlayers := 0
				skillRange := playlistBucket.matchmakerConfig.BaseSkillRange + (playlistBucket.matchmakerConfig.SkillIncrement * float64(baseTicket.Ticks))

				for i := 0; i < len(playlistBucket.tickets); i++ {
					if playlistBucket.tickets[i] == nil {
						continue
					}

					if playlistBucket.tickets[i].Skill() > baseTicket.Skill()+skillRange {
						endRangeIndex = i
						break
					}
					if playlistBucket.tickets[i].Skill() > baseTicket.Skill()-skillRange {
						rangePlayers += playlistBucket.tickets[i].PlayerCount()
						if startRangeIndex < 0 {
							startRangeIndex = i
						}
					}
				}

				if endRangeIndex == -1 {
					endRangeIndex = len(playlistBucket.tickets) - 1
				}

				if startRangeIndex < 0 || rangePlayers < matchSize {
					continue
				}

				for rangeTries := 0; rangeTries < playlistBucket.matchmakerConfig.MaxTries; rangeTries++ {
					ticketIndices := []int{baseTicketIndex}
					rangeFound := false
					rangeOrder := rand.Perm(endRangeIndex - startRangeIndex)

					slots := make([]int, teamCount)
					slots[0] = teamSize - baseTicket.PlayerCount()
					for t := 1; t < teamCount; t++ {
						slots[t] = teamSize
					}

					matchPlatform := baseTicket.Platform
					matchCanBeMixed := baseTicket.CanMatchMixed

					for i := 0; i < len(rangeOrder); i++ {
						checkTicketIndex := startRangeIndex + rangeOrder[i]
						if playlistBucket.tickets[checkTicketIndex] == nil || playlistBucket.tickets[checkTicketIndex] == baseTicket {
							continue
						}
						checkTicket := playlistBucket.tickets[checkTicketIndex]

						if !playlistBucket.matchmakerConfig.BypassPlatformRestrictions && !checkTicket.CanJoin(matchPlatform, matchCanBeMixed) {
							continue
						}

						for slot := 0; slot < teamCount; slot++ {
							if slots[slot] >= checkTicket.PlayerCount() {
								slots[slot] -= checkTicket.PlayerCount()
								ticketIndices = append(ticketIndices, checkTicketIndex)

								if matchPlatform == platformNone {
									matchPlatform = checkTicket.Platform
									matchCanBeMixed = checkTicket.CanMatchMixed
								} else if (matchPlatform == platformPlaystation && checkTicket.Platform == platformXbox) ||
									(matchPlatform == platformXbox && checkTicket.Platform == platformPlaystation) {
									matchPlatform = platformMixed
								} else if !checkTicket.CanMatchMixed {
									matchCanBeMixed = false
								}

								break
							}
						}

						full := true
						for slot := 0; slot < teamCount; slot++ {
							if slots[slot] > 0 {
								full = false
								break
							}
						}

						if !full {
							continue
						}

						avgTicks := 0.0

						for i := 0; i < len(ticketIndices); i++ {
							avgTicks += float64(playlistBucket.tickets[ticketIndices[i]].Ticks * playlistBucket.tickets[ticketIndices[i]].PlayerCount())
						}
						avgTicks /= float64(matchSize)

						bestScore := 100.0
						var bestMatch *matchmakerMatch = nil

						for i := 0; i < playlistBucket.matchmakerConfig.ShuffleTries; i++ {
							valid := true
							testMatch := NewMatchmakerMatch(teamCount)
							order := rand.Perm(len(ticketIndices))
							for j := 0; j < len(ticketIndices); j++ {
								fit := false
								addTicket := playlistBucket.tickets[ticketIndices[order[j]]]
								for t := 0; t < teamCount; t++ {
									if testMatch.Teams[t].PlayerCount()+addTicket.PlayerCount() <= teamSize {
										testMatch.Teams[t].Tickets = append(testMatch.Teams[t].Tickets, addTicket)
										fit = true
										break
									}
								}

								if !fit {
									valid = false
									break
								}
							}
							if valid {
								score := testMatch.Score(playlistBucket.skillBucket)
								if score >= 0 && score < bestScore {
									bestScore = score
									bestMatch = testMatch
								}
							}
						}

						targetScore := playlistBucket.matchmakerConfig.BaseTargetScore + (playlistBucket.matchmakerConfig.TargetScoreIncrement * avgTicks)
						if bestScore <= targetScore {
							bestMatch.MatchId = uuid.Must(uuid.NewRandom()).String()
							bestMatch.MapName, bestMatch.ModeName = playlistBucket.playlist.PickRandomMapMode()
							bestMatch.SkillBucket = playlistBucket.skillBucket.Name

							matches = append(matches, bestMatch)
							for _, index := range ticketIndices {
								playlistBucket.tickets[index] = nil
							}
							rangeFound = true
						}
						break
					}

					if rangeFound {
						break
					}
				}
			}
		}

		elapsed := time.Since(startTime).Seconds()
		logger.WithField("matchCount", len(matches)).
			WithField("environment", key.Environment).
			WithField("playlist", key.Playlist).
			WithField("build", key.Build).
			WithField("elapsed", elapsed).
			WithField("playerCount", playerCount).
			WithField("ticketCount", len(playlistBucket.tickets)).
			Info("Matchmaker pass complete")

		go m.sendMatchFoundEvents(ctx, logger, nk, key.Environment, key.Playlist, matches)

	MatchLoop:
		for _, match := range matches {
			candidate := make([]runtime.MatchmakerEntry, matchSize)
			i := 0
			for teamIndex, team := range match.Teams {
				for _, ticket := range team.Tickets {
					for _, player := range ticket.Players {
						if i >= matchSize {
							logger.WithField("match", match).Error("Too many players in match")
							continue MatchLoop
						}
						candidate[i] = player.Entry
						props := player.Entry.GetProperties()
						props[TeamNumberProperty] = teamIndex
						props[MatchIdProperty] = match.MatchId
						props[MapNameProperty] = match.MapName
						props[ModeNameProperty] = match.ModeName
						i++
					}
				}
			}

			results = append(results, candidate)
		}
	}

	return results
}

type matchFoundEvent struct {
	MatchId     string  `json:"matchId"`
	MapName     string  `json:"mapName"`
	ModeName    string  `json:"modeName"`
	Team        int     `json:"team"`
	SkillBucket string  `json:"skillBucket"`
	SkillMu     float64 `json:"skillMu"`
	SkillSigma  float64 `json:"skillSigma"`
	Environment string  `json:"environment"`
	Playlist    string  `json:"playlist"`
}

func (m *Matchmaker) sendMatchFoundEvents(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, environment string, playlist string, matches []*matchmakerMatch) {
	satori := nk.GetSatori()
	if satori == nil {
		return
	}

	for _, match := range matches {
		for teamIndex, team := range match.Teams {
			for _, ticket := range team.Tickets {
				for _, player := range ticket.Players {
					event := &matchFoundEvent{
						MatchId:     match.MatchId,
						MapName:     match.MapName,
						ModeName:    match.ModeName,
						Team:        teamIndex,
						SkillBucket: match.SkillBucket,
						SkillMu:     player.Rating.Mu,
						SkillSigma:  player.Rating.Sigma,
						Environment: environment,
						Playlist:    playlist,
					}

					satoriEvent, err := utils.MakeSatoriEvent("matchFound", event)
					if err != nil {
						logger.WithField("err", err).Error("error marshaling matchFound event")
						continue
					}

					satori.EventsPublish(ctx, player.Id, []*runtime.Event{satoriEvent})
				}
			}
		}
	}
}

type matchmakingStartEvent struct {
	Platform    string             `json:"platform"`
	Environment string             `json:"environment"`
	Playlist    string             `json:"playlist"`
	SkillBucket string             `json:"skillBucket"`
	SkillMu     float64            `json:"skillMu"`
	SkillSigma  float64            `json:"skillSigma"`
	Latencies   []utils.PingRegion `json:"latencies"`
}

func (m *Matchmaker) beforeMatchmakerAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	message, ok := in.Message.(*rtapi.Envelope_MatchmakerAdd)
	if !ok {
		logger.WithField("envelope", in).Error("unable to cast to matchmaker request")
		return nil, errors.New("unable to cast matchmaker request")
	}

	env := utils.GetPlayerEnvironment(ctx)
	message.MatchmakerAdd.StringProperties[EnvironmentProperty] = env

	userId := utils.GetUserId(ctx)
	platform := utils.GetPlayerPlatform(ctx)
	message.MatchmakerAdd.StringProperties[userId+PlatformPropertySuffix] = platform

	playlistName, found := message.MatchmakerAdd.StringProperties[PlaylistProperty]
	if !found {
		return nil, errors.New("playlist name missing")
	}

	_, skillBucket, _, err := m.PlaylistManager.GetPlaylistConfigs(playlistName, env)
	if err != nil {
		return nil, err
	}

	playerSkillBuckets, err := readSkill(ctx, logger, nk, env, []string{userId})
	if err != nil {
		logger.WithField("err", err).Error("error reading skill")
		return nil, err
	}

	skill := getSkillForBucket(skillBucket, playerSkillBuckets[userId])

	if message.MatchmakerAdd.NumericProperties == nil {
		message.MatchmakerAdd.NumericProperties = make(map[string]float64)
	}

	message.MatchmakerAdd.NumericProperties[userId+MuPropertySuffix] = skill.Mu
	message.MatchmakerAdd.NumericProperties[userId+SigmaPropertySuffix] = skill.Sigma

	playerMeta, err := m.PlayerMetaSystem.GetFromContext(ctx, logger)
	if err != nil {
		return nil, err
	}

	for _, region := range playerMeta.Ping {
		message.MatchmakerAdd.NumericProperties["R_"+region.Region+"_"+userId] = region.PingMs
	}

	startEvent, err := utils.MakeSatoriEvent("matchmakingStart", &matchmakingStartEvent{
		Platform:    platform,
		Environment: env,
		Playlist:    playlistName,
		SkillBucket: skillBucket.Name,
		SkillSigma:  skill.Sigma,
		SkillMu:     skill.Mu,
		Latencies:   playerMeta.Ping,
	})

	if err != nil {
		logger.WithField("err", err).Error("error creating matchmakingStart event")
		return nil, err
	}

	nk.GetSatori().EventsPublish(ctx, userId, []*runtime.Event{startEvent})

	// Hack for training mode
	if message.MatchmakerAdd.MinCount == 1 {
		message.MatchmakerAdd.MinCount = 2
		message.MatchmakerAdd.MaxCount = 2
	}

	return in, nil
}

func (m *Matchmaker) beforePartyMatchmakerAdd(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	message, ok := in.Message.(*rtapi.Envelope_PartyMatchmakerAdd)
	if !ok {
		logger.WithField("envelope", in).Error("unable to cast to matchmaker request")
		return nil, errors.New("unable to cast matchmaker request")
	}

	env := utils.GetPlayerEnvironment(ctx)
	message.PartyMatchmakerAdd.StringProperties[EnvironmentProperty] = env

	playlistName, found := message.PartyMatchmakerAdd.StringProperties[PlaylistProperty]
	if !found {
		return nil, errors.New("playlist name missing")
	}

	_, skillBucket, _, err := m.PlaylistManager.GetPlaylistConfigs(playlistName, env)
	if err != nil {
		return nil, err
	}

	partyId, node, _ := strings.Cut(message.PartyMatchmakerAdd.PartyId, ".")
	members, err := nk.StreamUserList(7, partyId, "", node, true, true)
	if err != nil {
		logger.WithField("err", err).WithField("partyId", partyId).Error("StreamUserList failed")
		return nil, err
	}

	userIds := make([]string, len(members))
	for i, member := range members {
		userIds[i] = member.GetUserId()

		if message.PartyMatchmakerAdd.NumericProperties == nil {
			message.PartyMatchmakerAdd.NumericProperties = make(map[string]float64)
		}
	}

	skills, err := readSkill(ctx, logger, nk, env, userIds)
	if err != nil {
		logger.WithField("err", err).Error("error reading skill")
		return nil, err
	}

	for _, member := range members {
		playerMeta, err := m.PlayerMetaSystem.Get(logger, member.GetUserId(), member.GetSessionId())
		if err != nil {
			return nil, err
		}

		message.PartyMatchmakerAdd.StringProperties[member.GetUserId()+PlatformPropertySuffix] = playerMeta.Platform

		for _, region := range playerMeta.Ping {
			message.PartyMatchmakerAdd.NumericProperties["R_"+region.Region+"_"+member.GetUserId()] = region.PingMs
		}

		playerSkill := getSkillForBucket(skillBucket, skills[member.GetUserId()])

		message.PartyMatchmakerAdd.NumericProperties[member.GetUserId()+MuPropertySuffix] = playerSkill.Mu
		message.PartyMatchmakerAdd.NumericProperties[member.GetUserId()+SigmaPropertySuffix] = playerSkill.Sigma

		startEvent, err := utils.MakeSatoriEvent("matchmakingStart", &matchmakingStartEvent{
			Platform:    playerMeta.Platform,
			Environment: env,
			Playlist:    playlistName,
			SkillBucket: skillBucket.Name,
			SkillSigma:  playerSkill.Sigma,
			SkillMu:     playerSkill.Mu,
			Latencies:   playerMeta.Ping,
		})

		if err != nil {
			logger.WithField("err", err).Error("error creating matchmakingStart event")
			return nil, err
		}

		nk.GetSatori().EventsPublish(ctx, member.GetUserId(), []*runtime.Event{startEvent})
	}

	// Hack for training mode
	if message.PartyMatchmakerAdd.MinCount == 1 {
		message.PartyMatchmakerAdd.MinCount = 2
		message.PartyMatchmakerAdd.MaxCount = 2
	}

	return in, nil
}

type pingResultsRequest struct {
	Regions []utils.PingRegion `json:"regions"`
}

func (m *Matchmaker) setPingResults(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var request pingResultsRequest
	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		logger.WithField("err", err).Error("Error parsing SetPingResults payload")
		return "", err
	}

	playerMeta, err := m.PlayerMetaSystem.GetFromContext(ctx, logger)
	if err != nil {
		logger.WithField("err", err).Error("Unable to retrieve player metadata")
		return "", err
	}

	playerMeta.Ping = request.Regions

	m.PlayerMetaSystem.Update(ctx, logger, playerMeta)

	logger.WithField("ping", request.Regions).Info("Updated player ping results")

	return "", nil
}

func (m *Matchmaker) submitPlayerRankingsRpc(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {

	var request submitPlayerRankingsRequest

	err := json.Unmarshal([]byte(payload), &request)
	if err != nil {
		logger.WithField("err", err).Error("Error parsing SubmitPlayerRankings payload")
		return "", err
	}

	logger.WithField("request", request).Info("SubmitPlayerRankings received")

	if request.Environment == "" || request.PlaylistName == "" {
		logger.Info("Environment and/or Playlist were not provided - skipping skill update")
		return "", nil
	}

	playerIds := []string{}
	for _, player := range request.PlayerRankings {
		playerIds = append(playerIds, player.OnlineId)
	}

	_, skillBucket, _, err := m.PlaylistManager.GetPlaylistConfigs(request.PlaylistName, request.Environment)
	if err != nil {
		logger.WithField("err", err).WithField("playlistName", request.PlaylistName).WithField("environment", request.Environment).Error("Could not get playlist configuration")
		return "", err
	}

	inputPlayers := make([]types.Team, len(playerIds))
	inputScores := make([]int, len(playerIds))

	skills, err := readSkill(ctx, logger, nk, request.Environment, playerIds)
	if err != nil {
		logger.WithField("err", err).WithField("playerIds", playerIds).Error("Could not read skill for players")
		return "", err
	}

	for i, player := range request.PlayerRankings {
		playerSkillBuckets, found := skills[player.OnlineId]
		if found {
			skill := getSkillForBucket(skillBucket, playerSkillBuckets)
			inputPlayers[i] = types.Team{
				types.Rating{
					Mu:    skill.Mu,
					Sigma: skill.Sigma,
					Z:     skillBucket.Z,
				},
			}
		} else {
			inputPlayers[i] = types.Team{
				types.Rating{
					Mu:    skillBucket.DefaultMu,
					Sigma: skillBucket.DefaultSigma,
					Z:     skillBucket.Z,
				},
			}
		}
		inputScores[i] = player.Score
	}

	options := *skillBucket.GetOptions()
	options.Score = inputScores

	newRatings := rating.Rate(inputPlayers, &options)

	for i := range newRatings {
		playerId := request.PlayerRankings[i].OnlineId
		if skills[playerId].Buckets == nil {
			skills[playerId] = newPlayerSkillRecord()
		}
		skills[playerId].Buckets[skillBucket.Name] = skillRecord{
			Mu:    newRatings[i][0].Mu,
			Sigma: newRatings[i][0].Sigma,
		}
	}

	logger.WithField("newRatings", skills).Info("Updated skill ratings after match")

	err = updateSkill(ctx, logger, nk, request.Environment, skills)
	if err != nil {
		logger.WithField("err", err).WithField("newRatings", skills).Error("Error updating skill ratings")
		return "", err
	}

	if skillBucket.UpdateSatoriProperty {
		satori := nk.GetSatori()
		if satori != nil {
			trueValue := true
			newProperties := runtime.PropertiesUpdate{
				Custom:    map[string]string{},
				Recompute: &trueValue,
			}

			for playerId, skill := range skills {
				for bucketName, bucket := range skill.Buckets {
					newProperties.Custom[fmt.Sprintf("skillMu-%s-%s", request.Environment, bucketName)] = strconv.FormatFloat(bucket.Mu, 'g', 8, 64)
					newProperties.Custom[fmt.Sprintf("skillSigma-%s-%s", request.Environment, bucketName)] = strconv.FormatFloat(bucket.Sigma, 'g', 8, 64)
				}

				satori.PropertiesUpdate(ctx, playerId, &newProperties)
			}
		}
	}

	return "", nil
}
