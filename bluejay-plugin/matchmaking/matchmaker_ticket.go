package matchmaking

import (
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
	"github.com/intinig/go-openskill/rating"
	"github.com/intinig/go-openskill/types"
	"wildlight.gg/bluejay/playlists"
)

type matchmakerTicket struct {
	Id            string
	Ticks         int
	Players       []*matchmakerPlayer
	Platform      platform
	CanMatchMixed bool
	Team          types.Team
}

func (t *matchmakerTicket) PlayerCount() int {
	return len(t.Players)
}

func (t *matchmakerTicket) Skill() float64 {
	if t.PlayerCount() == 1 {
		return rating.Ordinal(t.Team[0])
	}

	total := 0.0
	for _, playerRating := range t.Team {
		total += rating.Ordinal(playerRating)
	}

	return total / float64(len(t.Team))
}

func (t *matchmakerTicket) CanJoin(matchPlatform platform, matchCanBeMixed bool) bool {
	switch matchPlatform {
	case platformNone:
		return true
	case platformMixed:
		return (t.Platform == platformXbox || t.Platform == platformPlaystation) && t.CanMatchMixed
	case platformXbox:
		return (t.Platform == platformXbox) || (t.Platform == platformPlaystation && t.CanMatchMixed && matchCanBeMixed)
	case platformPlaystation:
		return (t.Platform == platformPlaystation) || (t.Platform == platformXbox && t.CanMatchMixed && matchCanBeMixed)
	case platformPC:
		return t.Platform == platformPC
	}

	return false
}

func getPlayerSkill(player runtime.MatchmakerEntry, skillBucket *playlists.SkillBucket, logger runtime.Logger) (float64, float64) {
	props := player.GetProperties()
	playerId := player.GetPresence().GetUserId()

	mu, muFound := getFloatProperty(playerId+MuPropertySuffix, props, skillBucket.DefaultMu)
	if !muFound {
		props[playerId+MuPropertySuffix] = mu
		logger.WithField("playerid", player.GetPresence().GetUserId()).WithField("mu", mu).Info("Setting default mu")
	}
	sigma, sigmaFound := getFloatProperty(playerId+SigmaPropertySuffix, props, skillBucket.DefaultSigma)
	if !sigmaFound {
		props[playerId+SigmaPropertySuffix] = sigma
		logger.WithField("playerid", player.GetPresence().GetUserId()).WithField("sigma", sigma).Info("Setting default sigma")
	}

	return mu, sigma
}

func makeMatchmakerTicket(players []runtime.MatchmakerEntry, skillBucket *playlists.SkillBucket, logger runtime.Logger) *matchmakerTicket {
	elapsed := time.Duration(time.Now().UnixNano() - players[0].GetCreateTime())

	newTicket := &matchmakerTicket{
		Id:      players[0].GetTicket(),
		Ticks:   int(elapsed.Seconds()),
		Players: make([]*matchmakerPlayer, len(players)),
		Team:    make([]types.Rating, len(players)),
	}

	for i, player := range players {
		mu, sigma := getPlayerSkill(player, skillBucket, logger)

		newTicket.Players[i] = &matchmakerPlayer{
			Id: player.GetPresence().GetUserId(),
			Rating: types.Rating{
				Mu:    mu,
				Sigma: sigma,
				Z:     skillBucket.Z,
			},
			Entry: player,
		}

		newTicket.Team[i] = newTicket.Players[i].Rating
	}

	return newTicket
}
