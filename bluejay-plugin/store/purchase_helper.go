package store

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"github.com/google/uuid"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/entitlements"
	"wildlight.gg/bluejay/errors"
	"wildlight.gg/bluejay/progression"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

// TransactionList Response object for loading users wallet ledger
type TransactionList struct {
	Transactions []runtime.WalletLedgerItem
	Cursor       string
}

type PurchaseSingleItemPayload struct {
	ItemID          string
	PaymentCurrency string
	PaymentAmount   int64
}

type PurchaseSingleTroveItemPayload struct {
	ItemID          string
	PaymentAmount   int64
	PaymentCurrency string
	TrovePageId     string
}

type PurchaseBundlePayload struct {
	BundleId        string
	PaymentCurrency string
	PaymentAmount   int64
}

type SectionLiteInfo struct {
	Comment        string
	Id             string
	TemplateType   int
	ExpirationTime int64
}

type SectionLiteInfoPayload struct {
	Sections []SectionLiteInfo
}

type BundleInfo struct {
	Id          string
	PricingInfo configs.PriceInfo
	Items       map[string]configs.ItemInfo
}

type SectionInfo struct {
	Id             string
	Bundles        map[string]BundleInfo
	TemplateType   int
	ExpirationTime int64
}

type AllSectionInfo struct {
	Sections map[string]SectionInfo
}

type SectionsListResponse struct {
	Sections []SectionInfo
}

type BundleListResponse struct {
	Bundles []BundleInfo
}

type ItemsListResponse struct {
	Items []configs.ItemInfo
}

type TreasureTroveDefinitionResponse struct {
	TreasureTroves map[string]configs.TreasureTroveDefinition
}

type TrovePageDefinitionResponse struct {
	TrovePages map[string]configs.TrovePageDefinition
}

type TrovePageCurrencies struct {
	Currencies map[string]int64
}

const (
	PURCHASE_REASON string = "purchase"
)

func processMultiUpdateForItemPurchase(ctx context.Context, nk runtime.NakamaModule, entitlementUpdate []*runtime.StorageWrite, walletUpdate []*runtime.WalletUpdate, logger runtime.Logger) (string, error) {
	logger.WithField("storage", entitlementUpdate).WithField("wallet", walletUpdate).Debug("Multi update of storage write and wallet updates")
	if storageAcks, walletUpdateResults, err := nk.MultiUpdate(ctx, []*runtime.AccountUpdate{}, entitlementUpdate, []*runtime.StorageDelete{}, walletUpdate, true); err != nil {
		logger.WithField("err", err).Error("Multi update error.")
		return "", err
	} else {
		logger.Info("Storage Acks: %d", len(storageAcks))
		logger.Info("Wallet Updates: %d", len(walletUpdateResults))

		if updateJSON, err := json.Marshal(entitlements.PurchaseResult{
			StorageUpdates: storageAcks,
			WalletUpdates:  walletUpdateResults,
		}); err != nil {
			logger.Error("Error formatting JSON string", err)
			return "", err
		} else {
			jsonString := string(updateJSON)
			return jsonString, nil
		}
	}
}

func createUserEntitlementUpdateForSingleItem(ctx context.Context, nk runtime.NakamaModule, itemId string, userID string, logger runtime.Logger) (runtime.StorageWrite, error) {

	userEntitlements, version, err := entitlements.ReadEntitlementsAsMap(ctx, nk, userID)

	if err != nil {
		return runtime.StorageWrite{}, err
	}

	if _, ok := userEntitlements.Entitlements[itemId]; ok {
		logger.Error(fmt.Sprintf("Item already exists for user. Cannot grant. %s", itemId))
		return runtime.StorageWrite{}, runtime.NewError(fmt.Sprintf("Item already exists for user. Cannot grant. %s", itemId), 13)
	}

	if userEntitlements.Entitlements == nil {
		userEntitlements.Entitlements = map[string]any{}
	}

	userEntitlements.Entitlements[itemId] = struct{}{}

	newEntitlements, err := json.Marshal(userEntitlements)

	if err != nil {
		return runtime.StorageWrite{}, err
	}

	storageWrite := runtime.StorageWrite{
		Collection:      storage.ENTITLEMENT_COLLECTION,
		Key:             storage.ENTITLEMENTS,
		UserID:          userID,
		PermissionRead:  1,
		PermissionWrite: 1,
		Value:           string(newEntitlements),
		Version:         version,
	}

	return storageWrite, nil
}

func generateWalletUpdateWithMetadataForSingleItem(itemId string, paymentCurrency string, paymentAmount int64, userID string, txID string) runtime.WalletUpdate {
	walletMetadata := []string{}

	walletMetadataMap := map[string]interface{}{}

	walletMetadata = append(walletMetadata, itemId)

	walletMetadataMap["items_purchased"] = walletMetadata
	walletMetadataMap["txID"] = txID

	walletChangeSet := make(map[string]int64)
	walletChangeSet[paymentCurrency] = -int64(paymentAmount)

	walletUpdate := runtime.WalletUpdate{
		UserID:    userID,
		Changeset: walletChangeSet,
		Metadata:  walletMetadataMap,
	}

	return walletUpdate
}

func createUpdateForSingleItem(ctx context.Context, nk runtime.NakamaModule, userID string, paymentCurrency string, paymentAmount int64, itemId string, txID string, logger runtime.Logger) (runtime.WalletUpdate, runtime.StorageWrite, error) {

	entitlementUpdate, err := createUserEntitlementUpdateForSingleItem(ctx, nk, itemId, userID, logger)

	if err != nil {
		return runtime.WalletUpdate{}, runtime.StorageWrite{}, err
	}

	walletUpdate := generateWalletUpdateWithMetadataForSingleItem(itemId, paymentCurrency, paymentAmount, userID, txID)

	return walletUpdate, entitlementUpdate, nil
}

func processUpdateForSingleItem(ctx context.Context, nk runtime.NakamaModule, userID string, paymentCurrency string, paymentAmount int64, itemId string, logger runtime.Logger) (string, error) {

	txID, err := uuid.NewV7()

	if err != nil {
		logger.WithField("err", err).Error("Error generating transaction id.")
		return "", err
	}

	if wUpdate, sUpdate, err := createUpdateForSingleItem(ctx, nk, userID, paymentCurrency, paymentAmount, itemId, txID.String(), logger); err != nil {
		logger.Error("Error creating update information", err)
		return "", err
	} else {

		response, err := processMultiUpdateForItemPurchase(ctx, nk, []*runtime.StorageWrite{&sUpdate}, []*runtime.WalletUpdate{&wUpdate}, logger)

		if err != nil {
			return "", err
		}

		err = publishWalletAndEntitlementSatoriEvents(ctx, nk, userID, logger, paymentCurrency, txID, paymentAmount, itemId)

		if err != nil {
			return "", err
		}

		return response, nil

	}
}

func publishWalletAndEntitlementSatoriEvents(ctx context.Context, nk runtime.NakamaModule, userID string, logger runtime.Logger, paymentCurrency string, txID uuid.UUID, paymentAmount int64, itemId string) error {
	err := publishWalletSatoriEvent(ctx, nk, userID, logger, paymentCurrency, txID, paymentAmount)
	if err != nil {
		return err
	}

	entitlementEvent := utils.EntitlementEvent{TransactionID: txID.String(), ProductID: itemId, Source: PURCHASE_REASON}
	utils.PublishSatoriEvent(ctx, logger, progression.ENTITLEMENT_UPDATE_EVENT, entitlementEvent)
	return nil
}

func publishWalletSatoriEvent(ctx context.Context, nk runtime.NakamaModule, userID string, logger runtime.Logger, paymentCurrency string, txID uuid.UUID, paymentAmount int64) error {
	acct, err := nk.AccountGetId(ctx, userID)

	if err != nil {
		logger.WithField("error", err).Error("Error getting user account info")
		return err
	}

	wallet := map[string]int64{}

	err = json.Unmarshal([]byte(acct.Wallet), &wallet)

	if err != nil {
		logger.WithField("error", err).WithField("wallet", acct.Wallet).Error("JSON parse error")
		return err
	}

	updatedBalance, exists := wallet[paymentCurrency]
	if !exists {
		updatedBalance = 0
	}

	// Calculate previous balance by adding back the payment amount (since it was spent)
	previousBalance := updatedBalance + paymentAmount

	walletEvent := utils.WalletEvent{
		TransactionID:   txID.String(),
		TransactionType: "sink",
		Reason:          PURCHASE_REASON,
		CurrencyType:    paymentCurrency,
		CurrencyAmount:  -int(paymentAmount),  // Negative because money is going out
		PreviousBalance: int(previousBalance), // Balance before purchase
		CurrencyBalance: int(updatedBalance),  // Balance after purchase (backward compatibility)
		UpdatedBalance:  int(updatedBalance),  // Balance after purchase (explicit)
	}
	utils.PublishSatoriEvent(ctx, logger, progression.WALLET_UPDATE_EVENT, walletEvent)
	return nil
}

func readPlayerTroveCurrency(ctx context.Context, nk runtime.NakamaModule, logger runtime.Logger, trovePageId string, userId string) (TrovePageCurrencies, string, error) {
	readRequest := &runtime.StorageRead{
		Collection: storage.TROVE_CURRENCY_COLLECTION,
		Key:        storage.TROVE_CURRENCY,
		UserID:     userId,
	}

	if readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return TrovePageCurrencies{}, "", runtime.NewError("Error read DB error.", 2)
	} else if len(readResult) == 0 {
		currencyAmount := TrovePageCurrencies{Currencies: map[string]int64{trovePageId: 0}}

		return currencyAmount, "", nil
	} else {
		var currencies TrovePageCurrencies
		if err := json.Unmarshal([]byte(readResult[0].Value), &currencies); err != nil {
			logger.Error(fmt.Sprintf("Could not unmarshall string into json %s", readResult[0].Value))
			return TrovePageCurrencies{}, "", err
		}

		return currencies, readResult[0].Version, nil
	}
}

func processUpdateForSingleTroveItem(ctx context.Context, nk runtime.NakamaModule, userID string, paymentCurrency string, paymentAmount int64, itemId string, trovePageId string, nextTrovePage string, trovePageTreshold int64, logger runtime.Logger) (string, error) {

	txID, err := uuid.NewV7()

	if err != nil {
		logger.WithField("err", err).Error("Error generating transaction id.")
		return "", err
	}

	if walletUpdate, entitlementUpdate, err := createUpdateForSingleItem(ctx, nk, userID, paymentCurrency, paymentAmount, itemId, txID.String(), logger); err != nil {
		logger.Error("Error creating update information", err)
		return "", err
	} else {

		// read users currency spent for the trove page
		trovePageCurrencies, version, err := readPlayerTroveCurrency(ctx, nk, logger, trovePageId, userID)

		if err != nil {
			return "", err
		}

		// add to it
		trovePageCurrencies.Currencies[trovePageId] = trovePageCurrencies.Currencies[trovePageId] + paymentAmount

		troveInfo, err := json.Marshal(trovePageCurrencies)

		if err != nil {
			return "", err
		}

		troveCurrencyUpdate := runtime.StorageWrite{
			Collection: storage.TROVE_CURRENCY_COLLECTION,
			Key:        storage.TROVE_CURRENCY,
			UserID:     userID,
			Value:      string(troveInfo),
			Version:    version,
		}

		newEntitlements := entitlements.EntitlementsUnlocked{IDs: []string{}}
		newEntitlements.IDs = append(newEntitlements.IDs, itemId)

		// if its more than the treshold, add the next page entitlement to it as well
		if len(nextTrovePage) != 0 && trovePageCurrencies.Currencies[trovePageId] >= trovePageTreshold {
			// No payment for this, its an unlock
			userEntitlementUpdate := entitlements.UserEntitlements{}
			err := json.Unmarshal([]byte(entitlementUpdate.Value), &userEntitlementUpdate)
			if err != nil {
				return "", err
			}

			userEntitlementUpdate.Entitlements[nextTrovePage] = struct{}{}

			newUserEntitlements, err := json.Marshal(userEntitlementUpdate)

			if err != nil {
				return "", err
			}

			entitlementUpdate.Value = string(newUserEntitlements)

			newEntitlements.IDs = append(newEntitlements.IDs, nextTrovePage)

		}

		_, err = processMultiUpdateForItemPurchase(ctx, nk, []*runtime.StorageWrite{&entitlementUpdate, &troveCurrencyUpdate}, []*runtime.WalletUpdate{&walletUpdate}, logger)

		if err != nil {
			return "", err
		}

		response, err := json.Marshal(newEntitlements)

		if err != nil {
			return "", err
		}

		if err != nil {
			return "", err
		}

		err = publishWalletAndEntitlementSatoriEvents(ctx, nk, userID, logger, paymentCurrency, txID, paymentAmount, itemId)

		if err != nil {
			return "", err
		}

		entitlementRewardEvents := []any{}

		for _, entitlement := range newEntitlements.IDs {

			event := utils.WarchestItemUnlocked{TransactionID: txID.String(), WarChestPieceID: entitlement, Reason: PURCHASE_REASON}

			entitlementRewardEvents = append(entitlementRewardEvents, event)
		}

		utils.PublishMultipleSatoriEvents(ctx, logger, progression.ENTITLEMENT_UPDATE_EVENT, entitlementRewardEvents)

		return string(response), err
	}
}

func createUserEntitlementUpdateForMultipleItems(ctx context.Context, nk runtime.NakamaModule, itemIds *[]string, userID string, logger runtime.Logger) ([]*runtime.StorageWrite, error) {

	storageUpdates := make([]*runtime.StorageWrite, 0)
	userEntitlements, version, err := entitlements.ReadEntitlementsAsMap(ctx, nk, userID)
	if err != nil {
		return nil, err
	}

	grantedItemIds := []string{}

	if userEntitlements.Entitlements == nil {
		userEntitlements.Entitlements = map[string]any{}
	}

	for _, itemId := range *itemIds {

		if _, ok := userEntitlements.Entitlements[itemId]; ok {
			logger.Warn(fmt.Sprintf("Item already exists for user. Not going to grant. %s", itemId))
		} else {
			logger.Info("Granting user the item! %s", itemId)
			grantedItemIds = append(grantedItemIds, itemId)

			userEntitlements.Entitlements[itemId] = struct{}{}
		}
	}

	newEntitlements, err := json.Marshal(userEntitlements)

	if err != nil {
		return nil, err
	}

	storageWrite := &runtime.StorageWrite{
		Collection:      storage.ENTITLEMENT_COLLECTION,
		Key:             storage.ENTITLEMENTS,
		UserID:          userID,
		PermissionRead:  1,
		PermissionWrite: 1,
		Value:           string(newEntitlements),
		Version:         version,
	}

	storageUpdates = append(storageUpdates, storageWrite)

	*itemIds = grantedItemIds

	return storageUpdates, nil
}

func generateWalletUpdateWithMetadataForMultipleItems(itemIds []string, paymentCurrency string, paymentAmount int64, userID string, txID string) runtime.WalletUpdate {
	walletMetadata := []string{}

	walletMetadataMap := map[string]interface{}{}

	walletMetadata = append(walletMetadata, itemIds...)

	walletMetadataMap["items_purchased"] = walletMetadata
	walletMetadataMap["txID"] = txID

	walletChangeSet := make(map[string]int64)
	walletChangeSet[paymentCurrency] = -int64(paymentAmount)

	walletUpdate := runtime.WalletUpdate{
		UserID:    userID,
		Changeset: walletChangeSet,
		Metadata:  walletMetadataMap,
	}

	return walletUpdate
}

func createUpdateForMultipleItems(ctx context.Context, nk runtime.NakamaModule, userID string, paymentCurrency string, paymentAmount int64, itemIds []string, txID string, logger runtime.Logger) ([]*runtime.WalletUpdate, []*runtime.StorageWrite, error) {

	entitlementUpdates, err := createUserEntitlementUpdateForMultipleItems(ctx, nk, &itemIds, userID, logger)

	if err != nil {
		return nil, nil, err
	}

	walletUpdates := generateWalletUpdateWithMetadataForMultipleItems(itemIds, paymentCurrency, paymentAmount, userID, txID)

	return []*runtime.WalletUpdate{&walletUpdates}, entitlementUpdates, nil

}

func processUpdateForMultipleItems(ctx context.Context, nk runtime.NakamaModule, userID string, paymentCurrency string, paymentAmount int64, bundleId string, items []string, logger runtime.Logger) (string, error) {

	// TODO: Grant topper items here as well when we have that

	logger.Info("Creating bundle Purchase request for bundle id %s, with items %v, using currency %s, for amount %d", bundleId, items, paymentCurrency, paymentAmount)
	txID, err := uuid.NewV7()

	if err != nil {
		logger.WithField("err", err).Error("Error generating transaction id.")
		return "", err
	}

	if wUpdate, sUpdate, err := createUpdateForMultipleItems(ctx, nk, userID, paymentCurrency, paymentAmount, items, txID.String(), logger); err != nil {
		logger.Error("Error creating update information", err)
		return "", err
	} else {

		response, err := processMultiUpdateForItemPurchase(ctx, nk, sUpdate, wUpdate, logger)

		if err != nil {
			return "", err
		}

		if err != nil {
			return "", err
		}

		err = publishWalletSatoriEvent(ctx, nk, userID, logger, paymentCurrency, txID, paymentAmount)
		if err != nil {
			return "", err
		}

		entitlementRewardEvents := []any{}

		for _, entitlement := range items {

			event := utils.EntitlementEvent{TransactionID: txID.String(), ProductID: entitlement, Source: PURCHASE_REASON}

			entitlementRewardEvents = append(entitlementRewardEvents, event)
		}

		utils.PublishMultipleSatoriEvents(ctx, logger, progression.ENTITLEMENT_UPDATE_EVENT, entitlementRewardEvents)

		return response, nil

	}
}

func ensureItemMeetsCriteria(ctx context.Context, itemId string, paymentAmount int64, paymentCurrency string, itemPriceInfo configs.PriceInfo, logger runtime.Logger, nk runtime.NakamaModule, userID string) error {

	// check to see the user has not already been granted the entitlements they are attempting to Purchase
	if alreadyHasItems, err := entitlements.HasEntitlements(ctx, nk, userID, []string{itemId}, logger); err != nil {
		logger.Error("Error fetching entitlement details", err)
		return err
	} else if alreadyHasItems {
		logger.Error("User already has one or more of the items they are trying to Purchase.", err)
		return errors.DataAlreadyExists
	} else {
		// verify user has balance to complete this Purchase

		account, err := nk.AccountGetId(ctx, userID)

		if err != nil {
			logger.Error(fmt.Sprintf("Got error while loading account info: %s", err.Error()))
			return err
		}

		wallet := map[string]int64{}

		if err := json.Unmarshal([]byte(account.Wallet), &wallet); err != nil {
			logger.Error(fmt.Sprintf("Got error while loading json wallet info: %s", err.Error()))

			return err
		}

		itemCurrency := paymentCurrency
		itemCost := itemPriceInfo.FullPrice

		if currentUserBalance, ok := wallet[itemCurrency]; !ok {
			return errors.InsufficientFunds
		} else if currentUserBalance < itemCost {
			return errors.InsufficientFunds
		} else if paymentAmount < itemCost {
			return errors.PaymentAmountInsufficient
		} else if paymentAmount > itemCost {
			return errors.PaymentAmountExceedsCost
		} else {
			return nil
		}

	}
}

func ensureBundleMeetsCriteria(ctx context.Context, bundleId string, paymentAmount int64, logger runtime.Logger, nk runtime.NakamaModule, userId string) (BundleInfo, error) {
	if bundlesMap, err := loadBundles(ctx); err != nil {
		logger.Error("Error calling loadItems", err)
		return BundleInfo{}, err
	} else
	// Verify bundle in users request also exist
	if bundleInfo, ok := bundlesMap[bundleId]; !ok {
		return BundleInfo{}, errors.BundleNotFound
	} else {

		// verify user has balance to complete this Purchase
		account, err := nk.AccountGetId(ctx, userId)

		if err != nil {
			logger.Error(fmt.Sprintf("Got error while loading account info: %s", err.Error()))
			return BundleInfo{}, err
		}

		wallet := map[string]int64{}

		if err := json.Unmarshal([]byte(account.Wallet), &wallet); err != nil {
			logger.Error(fmt.Sprintf("Got error while loading json wallet info: %s", err.Error()))

			return BundleInfo{}, err
		}

		bundleCurrency := bundleInfo.PricingInfo.CurrencyType

		logger.Info("Current user wallet balance for currency %s : %d", bundleCurrency, wallet[bundleCurrency])

		// Calculate cost of bundle if user already owns one or more items in the bundle
		logger.Info("Processing section and bundle info to update with discount")

		if bundleInfo, err := discountBundle(ctx, nk, userId, bundleInfo, logger); err != nil {
			return BundleInfo{}, err
		} else {

			// Check against the "discounted price" because that is value based on items already owned.
			// If they own none of the items, then base price and discounted price is the same
			if currentUserBalance, ok := wallet[bundleCurrency]; !ok {
				return BundleInfo{}, errors.InsufficientFunds
			} else if currentUserBalance < bundleInfo.PricingInfo.UserPrice {
				return BundleInfo{}, errors.InsufficientFunds
			} else if paymentAmount < bundleInfo.PricingInfo.UserPrice {
				return BundleInfo{}, errors.PaymentAmountInsufficient
				// Disabled over paying check for First Playable. Must renable ASAP.
				// } else if bundle.PaymentAmount > bundleInfo.PricingInfo.UserPrice {
				// 	return BundleInfo{}, PaymentAmountExceedsCost
			} else {
				return bundleInfo, nil
			}
		}

	}
}

func discountBundle(ctx context.Context, nk runtime.NakamaModule, userId string, bundleInfo BundleInfo, logger runtime.Logger) (BundleInfo, error) {

	if userEntitlements, err := entitlements.ReadEntitlements(ctx, nk, userId); err != nil {
		return BundleInfo{}, err
	} else {
		entitlementMap := map[string]struct{}{}

		for _, entitlement := range userEntitlements.IDs {
			entitlementMap[entitlement] = struct{}{}
		}

		// Grab the current price of the bundle
		currentBundlePrice := bundleInfo.PricingInfo.FullPrice
		basePrice := bundleInfo.PricingInfo.FullPrice

		// (not using SalePrice for now, but for future)
		// If the bundle is on sale, use that instead
		// if bundleInfo.PricingInfo.SalePrice > -1 {
		// 	currentBundlePrice = bundleInfo.PricingInfo.SalePrice
		// 	basePrice = bundleInfo.PricingInfo.SalePrice
		// }

		// Roll up value for total price if each item is to be Purchased individually
		var totalItemsPrice int64 = 0
		itemsInBundle := []string{}

		itemsOwnedFromBundle := []string{}

		// Go through the items in the bunndle, add up their price
		for itemId, itemInfo := range bundleInfo.Items {
			totalItemsPrice += itemInfo.PricingInfo.FullPrice
			itemsInBundle = append(itemsInBundle, itemId)
		}

		for _, itemid := range itemsInBundle {
			logger.Info("Checking to see if user has the item %s", itemid)
			if _, ok := entitlementMap[itemid]; !ok {
				// user does not own item, skip
			} else {
				itemsOwnedFromBundle = append(itemsOwnedFromBundle, itemid)
				// user does own item
				logger.Info("User does have this item! Lets discount the bundle for them.")
				logger.Info("There are %d items in this bundle. Total price for them all is %d. Bundle price is %d. Discounted price is %d", len(itemsInBundle), totalItemsPrice, basePrice, currentBundlePrice)

				if itemInfo, ok := bundleInfo.Items[itemid]; !ok {
					logger.Error("Somehow the item doesn't exist in the bundle anymore?")
					return BundleInfo{}, errors.ItemNotFoundInBundle
				} else {
					// How much is this item worth out of the whole price?
					itemPrice := itemInfo.PricingInfo.FullPrice

					// Example:
					// Total item price 3000, 3 items in total each 1000, bundle price 2000
					// If you buy 1 item, then the calculation is (1000 / 3000) * 2000 = 666.66 repeating
					// then we lower the current price by the discount amount. if we own all 3 items, then the current price should be 0.
					percentDiscount := math.Round((float64(itemPrice)/float64(totalItemsPrice))*100) / 100
					discount := int64(percentDiscount * float64(basePrice))
					logger.Info("%d := int64((float64(%d) / float64(%d)) * float64(%d))", discount, itemPrice, totalItemsPrice, basePrice)

					// currentBundlePrice, with the above example if one item valued at 1000 was Purchased, would be 1334.
					logger.Info("%d -= %d", currentBundlePrice, discount)
					currentBundlePrice -= discount
					logger.Info("currentBundlePrice: %d", currentBundlePrice)
				}
			}
		}

		// If user owns all the items in the bundle, lets lower the price all the way to 0
		// to avoid any issues where we are discounting by 1/3 or other irrational ratios
		if len(bundleInfo.Items) == len(itemsOwnedFromBundle) {
			bundleInfo.PricingInfo.UserPrice = 0
		} else {
			bundleInfo.PricingInfo.UserPrice = currentBundlePrice
		}

		logger.Info("Setting new bundle price: full %d, sale %d to %d", bundleInfo.PricingInfo.FullPrice, bundleInfo.PricingInfo.SalePrice, bundleInfo.PricingInfo.UserPrice)
		return bundleInfo, nil
	}
}

func loadStoreSections() SectionLiteInfoPayload {
	sections := []SectionLiteInfo{}

	sections = append(sections, SectionLiteInfo{
		Comment:        "feature_page_1",
		Id:             "39795F95406C91EBA7DA9EB3EED1B37C",
		TemplateType:   0,
		ExpirationTime: 253402329599,
	})

	sections = append(sections, SectionLiteInfo{
		Comment:        "bundle_page_1",
		Id:             "1874ED314400973CBD93369C40AF0325",
		TemplateType:   1,
		ExpirationTime: 253402329599,
	})

	sections = append(sections, SectionLiteInfo{
		Comment:        "feature_page_2",
		Id:             "BDE2335C428B12B2903B7B81D9BF1DBD",
		TemplateType:   0,
		ExpirationTime: 253402329599,
	})

	sections = append(sections, SectionLiteInfo{
		Comment:        "bundle_page_2",
		Id:             "DFDB17DA4A577852064EF6BEBE06DA37",
		TemplateType:   1,
		ExpirationTime: 253402329599,
	})

	sections = append(sections, SectionLiteInfo{
		Comment:        "event_page",
		Id:             "3E08165042E3027AE932C79D724B9306",
		TemplateType:   0,
		ExpirationTime: 253402329599,
	})

	return SectionLiteInfoPayload{Sections: sections}
}

func loadItems(ctx context.Context) (map[string]configs.ItemInfo, error) {
	if storeData, err := configs.LoadStoreConfig(ctx); err != nil {
		return map[string]configs.ItemInfo{}, err
	} else {

		return storeData.Items, nil
	}

}

func loadBundles(ctx context.Context) (map[string]BundleInfo, error) {

	bundlesMap := map[string]BundleInfo{}

	storeData, err := configs.LoadStoreConfig(ctx)

	if err != nil {
		return bundlesMap, err
	}

	bundlesDefinitionsMap := storeData.Bundles

	if itemsMap, err := loadItems(ctx); err != nil {
		return bundlesMap, err
	} else {
		for bundleId, bundleDef := range bundlesDefinitionsMap {

			bundleInfo := BundleInfo{Id: bundleId, PricingInfo: bundleDef.PricingInfo, Items: map[string]configs.ItemInfo{}}
			for _, itemId := range bundleDef.Items {
				if itemInfo, ok := itemsMap[itemId]; !ok {
					return bundlesMap, errors.ItemNotFound(itemId)
				} else {
					bundleInfo.Items[itemId] = itemInfo
				}
			}

			bundlesMap[bundleId] = bundleInfo

		}
	}

	return bundlesMap, nil
}

func loadSectionsByIdsInfo(ctx context.Context, sectionIds []string) ([]SectionInfo, error) {
	sections := []SectionInfo{}

	storeData, err := configs.LoadStoreConfig(ctx)

	if err != nil {
		return sections, err
	}

	sectionDefinitionsMap := storeData.Sections

	if bundlesMap, err := loadBundles(ctx); err != nil {
		return []SectionInfo{}, errors.BundleNotFound
	} else {
		for _, sectionid := range sectionIds {
			if sectionDef, ok := sectionDefinitionsMap[sectionid]; !ok {
				return []SectionInfo{}, errors.SectionNotFound
			} else {

				section := SectionInfo{Bundles: map[string]BundleInfo{}, Id: sectionid, TemplateType: sectionDef.TemplateType, ExpirationTime: sectionDef.ExpirationTime}

				for _, bundleId := range sectionDef.Bundles {
					bundleInfo := bundlesMap[bundleId]
					section.Bundles[bundleId] = bundleInfo
				}

				sections = append(sections, section)
			}
		}
	}

	return sections, nil
}

func loadTreasureTroves(ctx context.Context) (map[string]configs.TreasureTroveDefinition, error) {
	if storeData, err := configs.LoadStoreConfig(ctx); err != nil {
		return map[string]configs.TreasureTroveDefinition{}, err
	} else {

		return storeData.TreasureTroves, nil
	}

}

func loadTrovesPages(ctx context.Context) (map[string]configs.TrovePageDefinition, error) {
	if storeData, err := configs.LoadStoreConfig(ctx); err != nil {
		return map[string]configs.TrovePageDefinition{}, err
	} else {

		return storeData.TrovePages, nil
	}

}

func loadCurrencyLimits(ctx context.Context) (configs.CurrencyLimits, error) {
	if storeData, err := configs.LoadStoreConfig(ctx); err != nil {
		return configs.CurrencyLimits{}, err
	} else {
		return configs.CurrencyLimits{Limits: storeData.CurrencyMaxLimits}, nil
	}
}
