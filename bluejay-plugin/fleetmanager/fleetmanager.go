package fleetmanager

import (
	"bytes"
	"context"
	"crypto/tls"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/utils"
)

type WildlightFleetManager struct {
	ctx              context.Context
	logger           runtime.Logger
	nk               runtime.NakamaModule
	db               *sql.DB
	httpClient       *http.Client
	callbackHandler  runtime.FmCallbackHandler
	serverManagerUrl string
}

func NewWildlightFleetManager(ctx context.Context, logger runtime.Logger, db *sql.DB, initializer runtime.Initializer, nk runtime.NakamaModule) (runtime.FleetManagerInitializer, error) {
	env, ok := ctx.Value(runtime.RUNTIME_CTX_ENV).(map[string]string)
	if !ok {
		return nil, errors.New("expected RUNTIME_CTX_ENV to be a map[string]string")
	}

	url, found := env["NC_SERVERMANAGER_URL"]
	if !found {
		return nil, errors.New("missing configuration: NC_SERVERMANAGER_URL")
	}

	fleetManager := &WildlightFleetManager{
		ctx:              ctx,
		db:               db,
		logger:           logger.WithField("mod", "FleetManager"),
		nk:               nk,
		serverManagerUrl: url,
	}

	err := utils.RegisterWildlightRpc(initializer, logger, "update_instance_info", fleetManager.UpdateInstanceInfo)
	if err != nil {
		logger.Error("Failed to register FleetManager update_instance_info rpc: %v", err)
		return nil, err
	}

	err = utils.RegisterWildlightRpc(initializer, logger, "delete_instance_info", fleetManager.DeleteInstanceInfo)
	if err != nil {
		logger.Error("Failed to register FleetManager delete_instance_info rpc: %v", err)
		return nil, err
	}

	return fleetManager, nil
}

func (fleetManager *WildlightFleetManager) Init(nk runtime.NakamaModule, callbackHandler runtime.FmCallbackHandler) error {
	fleetManager.nk = nk
	fleetManager.callbackHandler = callbackHandler

	fleetManager.logger.Info("Wildlight fleet manager initialized")

	insecureTransport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	fleetManager.httpClient = &http.Client{Transport: insecureTransport}

	return nil
}

type allocateRequest struct {
	Region       string                       `json:"region,omitempty"`
	BuildVersion string                       `json:"buildVersion"`
	MatchId      string                       `json:"matchId"`
	MapName      string                       `json:"mapName"`
	ModeName     string                       `json:"modeName,omitempty"`
	LaunchToken  string                       `json:"launchToken"`
	UserIds      []string                     `json:"userIds,omitempty"`
	Latencies    []runtime.FleetUserLatencies `json:"latencies,omitempty"`
}

type allocateResponse struct {
	InstanceId  string `json:"instanceId"`
	Ipv4Address string `json:"ipv4address"`
	Ipv6Address string `json:"ipv6address"`
	Port        int    `json:"port"`
}

const (
	MetadataRegion       string = "region"
	MetadataMapName      string = "mapName"
	MetadataModeName     string = "modeName"
	MetadataBuildVersion string = "buildVersion"
	MetadataLaunchToken  string = "launchToken"
	MetadataMatchId      string = "matchId"
)

func (fleetManager *WildlightFleetManager) Create(ctx context.Context, maxPlayers int, userIds []string, latencies []runtime.FleetUserLatencies, metadata map[string]any, callback runtime.FmCreateCallbackFn) error {
	requestBody := allocateRequest{}
	region, found := metadata[MetadataRegion]
	if found {
		requestBody.Region = region.(string)
	}

	mapName, found := metadata[MetadataMapName]
	if found {
		requestBody.MapName = mapName.(string)
	}

	modeName, found := metadata[MetadataModeName]
	if found {
		requestBody.ModeName = modeName.(string)
	}

	buildVersion, found := metadata[MetadataBuildVersion]
	if found {
		requestBody.BuildVersion = buildVersion.(string)
	}

	launchToken, found := metadata[MetadataLaunchToken]
	if found {
		requestBody.LaunchToken = launchToken.(string)
	}

	matchId, found := metadata[MetadataMatchId]
	if found {
		requestBody.MatchId = matchId.(string)
	}

	requestBody.Latencies = latencies
	requestBody.UserIds = userIds

	requestJson, err := json.Marshal(requestBody)
	if err != nil {
		return err
	}

	request, err := http.NewRequest(http.MethodPut, fmt.Sprintf("%s/dedicatedserver/v1/allocate", fleetManager.serverManagerUrl), bytes.NewBuffer(requestJson))
	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", "application/json")

	go func() {
		response, err := fleetManager.httpClient.Do(request)
		if err != nil {
			fleetManager.logger.Error("error making HTTP PUT request: %v", err)
			callback(runtime.CreateError, nil, nil, nil, err)
			return
		}

		if response.StatusCode != 200 {
			fleetManager.logger.Warn("failed to allocate instance: %v", err)
			callback(runtime.CreateError, nil, nil, nil, errors.New("no capacity"))
			return
		} else {
			var allocation allocateResponse
			err := json.NewDecoder(response.Body).Decode(&allocation)
			if err != nil {
				fleetManager.logger.Error("failed to read json allocateResponse: %v", err)
				callback(runtime.CreateError, nil, nil, nil, err)
				return
			}

			fleetManager.logger.Info("[FleetManager] instance allocated: ID=%s Addr=%s:%d", allocation.InstanceId, allocation.Ipv4Address, allocation.Port)
			instance := &runtime.InstanceInfo{
				Id: allocation.InstanceId,
				ConnectionInfo: &runtime.ConnectionInfo{
					IpAddress: allocation.Ipv4Address,
					DnsName:   allocation.Ipv6Address, // hax
					Port:      allocation.Port,
				},
			}

			callback(runtime.CreateSuccess, instance, nil, nil, nil)
		}
	}()

	return nil
}

type instance struct {
	Name                string `json:"name"`
	Region              string `json:"region"`
	Ipv4Address         string `json:"ipv4Address"`
	Ipv6Address         string `json:"ipv6Address,omitempty"`
	Port                int32  `json:"port"`
	MapName             string `json:"mapName"`
	ModeName            string `json:"modeName"`
	BuildVersion        string `json:"buildVersion"`
	IsLocalBuild        bool   `json:"isLocalBuild,omitempty"`
	PlayerCount         int32  `json:"playerCount"`
	MaxPlayerCount      int32  `json:"maxPlayerCount"`
	IsInMatchmakingPool bool   `json:"isInMatchmakingPool,omitempty"`
	IsAllocated         bool   `json:"isAllocated,omitempty"`
	IsStarting          bool   `json:"isStarting,omitempty"`
}

func (fleetManager *WildlightFleetManager) Get(ctx context.Context, id string) (*runtime.InstanceInfo, error) {
	request, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s/dedicatedserver/v1/server/%s", fleetManager.serverManagerUrl, id), nil)
	if err != nil {
		return nil, err
	}

	response, err := fleetManager.httpClient.Do(request)
	if err != nil {
		fleetManager.logger.Error("error making HTTP GET request: %v", err)
		return nil, err
	}

	if response.StatusCode == 200 {
		var instance instance
		err := json.NewDecoder(response.Body).Decode(&instance)
		if err != nil {
			fleetManager.logger.Error("error decoding GetServer response: %v", err)
			return nil, err
		}

		info := &runtime.InstanceInfo{
			Id: id,
			ConnectionInfo: &runtime.ConnectionInfo{
				IpAddress: instance.Ipv4Address,
				DnsName:   instance.Ipv6Address,
				Port:      int(instance.Port),
			},
			PlayerCount: int(instance.PlayerCount),
		}

		if instance.IsStarting {
			info.Status = "starting"
		} else {
			info.Status = "ready"
		}

		return info, nil
	}

	return nil, errors.New("[FleetManager] Get: Not implemented")
}

func (fleetManager *WildlightFleetManager) List(ctx context.Context, query string, limit int, prevCursor string) (list []*runtime.InstanceInfo, nextCursor string, err error) {
	return nil, "", errors.New("[FleetManager] List: Not implemented")
}

func (fleetManager *WildlightFleetManager) Join(ctx context.Context, id string, userIds []string, metadata map[string]string) (joinInfo *runtime.JoinInfo, err error) {
	return nil, errors.New("[FleetManager] Join: Not implemented")
}

func (fleetManager *WildlightFleetManager) UpdateInstanceInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payuload string) (string, error) {
	return "", errors.New("[FleetManager] UpdateInstanceInfo: Not implemented")
}

func (fleetManager *WildlightFleetManager) DeleteInstanceInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return "", errors.New("[FleetManager] DeleteInstanceInfo: Not implemented")
}

func (fleetManager *WildlightFleetManager) Update(ctx context.Context, id string, playerCount int, metadata map[string]any) error {
	return errors.New("[FleetManager] Update: Not implemented")
}

func (fleetManager *WildlightFleetManager) Delete(ctx context.Context, id string) error {
	return errors.New("[FleetManager] Delete: Not implemented")
}
