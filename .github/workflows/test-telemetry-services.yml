name: Test telemetry-services

on:
  pull_request:
    paths:
      - "telemetry-services/**"

jobs:
  test:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: telemetry-services

    steps:
      - uses: actions/checkout@v4

      - name: Run tests in Dockerfile
        # just running tests so only need build stage
        run: docker build --target build --build-arg RunTests=true .
