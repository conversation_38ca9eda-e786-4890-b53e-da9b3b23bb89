name: "Push nakama Container Image"

on:
  workflow_dispatch:

  push:
    paths:
      - "nakama/**"
      - "nakama-build/**"
      - "bluejay-plugin/**"
    branches:
      - "main"

jobs:
  aws-ecr-push:
    name: 'Call aws-ecr-push'
    uses: wildlight-entertainment/shared-gh-actions/.github/workflows/aws-ecr-push.yml@d0be8e2b57463515fc10da4c45b5f911e20fc445
    permissions:
      id-token: write
      contents: read
    with:
      image-title: <PERSON><PERSON><PERSON>
      repo-name: bluejay/nakama
      aws-github-role-arn: ${{ vars.AWS_STUDIO_INFRA_GITHUB_ROLE_ARN }}
      dockerfile: nakama-build/Dockerfile
      version-file: nakama/version.yml
      version-folder-paths: |
        "nakama"
        "bluejay-plugin"
