name: "Push servermanager Container Image"

on:
  workflow_dispatch:

  push:
    paths:
      - "servermanager/**"
    branches:
      - "main"

jobs:
  aws-ecr-push:
    name: 'Call aws-ecr-push'
    uses: wildlight-entertainment/shared-gh-actions/.github/workflows/aws-ecr-push.yml@d0be8e2b57463515fc10da4c45b5f911e20fc445
    permissions:
      id-token: write
      contents: read
    with:
      image-title: Server Manager
      repo-name: bluejay/servermanager
      aws-github-role-arn: ${{ vars.AWS_STUDIO_INFRA_GITHUB_ROLE_ARN }}
      context-directory: servermanager
      dockerfile: servermanager/Dockerfile
      version-file: servermanager/version.yml
