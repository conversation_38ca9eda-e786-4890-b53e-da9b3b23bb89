name: "Push telemetry-services Container Image"

on:
  workflow_dispatch:

  push:
    paths:
      - "telemetry-services/**"
    branches:
      - "main"

jobs:
  aws-ecr-push:
    name: 'Call aws-ecr-push'
    uses: wildlight-entertainment/shared-gh-actions/.github/workflows/aws-ecr-push.yml@d0be8e2b57463515fc10da4c45b5f911e20fc445
    permissions:
      id-token: write
      contents: read
    with:
      image-title: Telemetry
      repo-name: bluejay/telemetry
      aws-github-role-arn: ${{ vars.AWS_STUDIO_INFRA_GITHUB_ROLE_ARN }}
      context-directory: telemetry-services
      dockerfile: telemetry-services/Dockerfile
      version-file: telemetry-services/version.yml
